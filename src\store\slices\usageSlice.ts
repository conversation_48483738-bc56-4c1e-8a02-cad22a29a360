// Usage Slice for Redux Store

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UsageEntry, UsageState } from '@/types';
import { ElectricityCalculator } from '@/utils/calculations';

// Async thunks
export const addUsage = createAsyncThunk(
  'usage/add',
  async (usageData: {
    previousUnits: number;
    currentUnits: number;
    notes?: string;
  }) => {
    const usage: UsageEntry = {
      id: Date.now().toString(),
      userId: 'current-user',
      date: new Date(),
      previousUnits: usageData.previousUnits,
      currentUnits: usageData.currentUnits,
      usageAmount: ElectricityCalculator.calculateUsage(
        usageData.previousUnits,
        usageData.currentUnits
      ),
      notes: usageData.notes,
      timestamp: Date.now(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return usage;
  }
);

// Initial state
const initialState: UsageState = {
  entries: [],
  currentUnits: 0,
  isLoading: false,
  error: null,
  totals: {
    weekly: 0,
    monthly: 0,
  },
};

// Usage slice
const usageSlice = createSlice({
  name: 'usage',
  initialState,
  reducers: {
    setUsage: (state, action: PayloadAction<UsageEntry[]>) => {
      state.entries = action.payload;
      state.error = null;
    },
    addUsageLocal: (state, action: PayloadAction<UsageEntry>) => {
      state.entries.unshift(action.payload);
      state.currentUnits = action.payload.currentUnits;
      state.error = null;
    },
    updateCurrentUnits: (state, action: PayloadAction<number>) => {
      state.currentUnits = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    updateTotals: (state, action: PayloadAction<{ weekly: number; monthly: number }>) => {
      state.totals = action.payload;
    },
    clearUsage: (state) => {
      state.entries = [];
      state.currentUnits = 0;
      state.totals = { weekly: 0, monthly: 0 };
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addUsage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addUsage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entries.unshift(action.payload);
        state.currentUnits = action.payload.currentUnits;
        state.error = null;
      })
      .addCase(addUsage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to add usage';
      });
  },
});

export const {
  setUsage,
  addUsageLocal,
  updateCurrentUnits,
  setError,
  clearError,
  setLoading,
  updateTotals,
  clearUsage,
} = usageSlice.actions;

export default usageSlice.reducer;

// Selectors
export const selectUsage = (state: { usage: UsageState }) => state.usage.entries;
export const selectUsageLoading = (state: { usage: UsageState }) => state.usage.isLoading;
export const selectUsageError = (state: { usage: UsageState }) => state.usage.error;
export const selectUsageTotals = (state: { usage: UsageState }) => state.usage.totals;
export const selectUsageCurrentUnits = (state: { usage: UsageState }) => state.usage.currentUnits;
