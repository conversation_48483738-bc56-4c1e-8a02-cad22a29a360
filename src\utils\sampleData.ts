// Sample Data for Development and Testing

import { PurchaseEntry, UsageEntry, UserSettings } from '@/types';
import { DEFAULT_SETTINGS } from '@/constants';

// Sample user settings
export const sampleUserSettings: UserSettings = {
  id: 'sample-user-1',
  userId: 'sample-user-1',
  unitType: 'Units',
  currency: 'USD',
  costPerUnit: 0.15,
  thresholdLimit: 10,
  theme: 'electric-blue',
  fontFamily: 'default',
  notificationsEnabled: true,
  notificationTime: '18:00',
  monthlyResetEnabled: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Sample purchase entries
export const samplePurchases: PurchaseEntry[] = [
  {
    id: 'purchase-1',
    userId: 'sample-user-1',
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    currencyAmount: 50.00,
    unitsPurchased: 333.33,
    costPerUnit: 0.15,
    currency: 'USD',
    notes: 'Monthly electricity purchase',
    timestamp: Date.now() - 2 * 24 * 60 * 60 * 1000,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
  },
  {
    id: 'purchase-2',
    userId: 'sample-user-1',
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    currencyAmount: 25.00,
    unitsPurchased: 166.67,
    costPerUnit: 0.15,
    currency: 'USD',
    notes: 'Emergency top-up',
    timestamp: Date.now() - 7 * 24 * 60 * 60 * 1000,
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
  },
  {
    id: 'purchase-3',
    userId: 'sample-user-1',
    date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
    currencyAmount: 75.00,
    unitsPurchased: 500.00,
    costPerUnit: 0.15,
    currency: 'USD',
    timestamp: Date.now() - 14 * 24 * 60 * 60 * 1000,
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
  },
];

// Sample usage entries
export const sampleUsage: UsageEntry[] = [
  {
    id: 'usage-1',
    userId: 'sample-user-1',
    date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    previousUnits: 50.0,
    currentUnits: 45.7,
    usageAmount: 4.3,
    notes: 'Daily usage reading',
    timestamp: Date.now() - 1 * 24 * 60 * 60 * 1000,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
  },
  {
    id: 'usage-2',
    userId: 'sample-user-1',
    date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    previousUnits: 65.2,
    currentUnits: 50.0,
    usageAmount: 15.2,
    notes: 'Weekend usage - higher consumption',
    timestamp: Date.now() - 3 * 24 * 60 * 60 * 1000,
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
  },
  {
    id: 'usage-3',
    userId: 'sample-user-1',
    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    previousUnits: 78.5,
    currentUnits: 65.2,
    usageAmount: 13.3,
    timestamp: Date.now() - 5 * 24 * 60 * 60 * 1000,
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
  },
];

// Function to initialize sample data
export const initializeSampleData = () => {
  return {
    user: sampleUserSettings,
    purchases: samplePurchases,
    usage: sampleUsage,
  };
};

// Function to calculate current units from sample data
export const calculateCurrentUnitsFromSample = () => {
  const totalPurchased = samplePurchases.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0);
  const totalUsed = sampleUsage.reduce((sum, usage) => sum + usage.usageAmount, 0);
  return Math.max(0, totalPurchased - totalUsed);
};

export default {
  sampleUserSettings,
  samplePurchases,
  sampleUsage,
  initializeSampleData,
  calculateCurrentUnitsFromSample,
};
