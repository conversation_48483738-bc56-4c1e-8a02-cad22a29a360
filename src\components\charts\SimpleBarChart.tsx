// Simple Bar Chart Component - Basic implementation without external libraries

import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';

interface DataPoint {
  label: string;
  value: number;
  color?: string;
}

interface SimpleBarChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  title?: string;
  showValues?: boolean;
  maxValue?: number;
}

const SimpleBarChart: React.FC<SimpleBarChartProps> = ({
  data,
  width = Dimensions.get('window').width - 40,
  height = 200,
  title,
  showValues = true,
  maxValue,
}) => {
  const chartHeight = height - 60; // Reserve space for labels
  const barWidth = (width - 40) / data.length - 10; // Space between bars
  const max = maxValue || Math.max(...data.map(d => d.value));

  const renderBar = (item: DataPoint, index: number) => {
    const barHeight = max > 0 ? (item.value / max) * chartHeight : 0;
    const barColor = item.color || '#007AFF';

    return (
      <View key={index} style={[styles.barContainer, { width: barWidth }]}>
        <View style={styles.barWrapper}>
          {showValues && (
            <Text style={styles.valueText}>{item.value.toFixed(1)}</Text>
          )}
          <View
            style={[
              styles.bar,
              {
                height: barHeight,
                backgroundColor: barColor,
                width: barWidth - 4,
              },
            ]}
          />
        </View>
        <Text style={styles.labelText} numberOfLines={2}>
          {item.label}
        </Text>
      </View>
    );
  };

  return (
    <View style={[styles.container, { width, height }]}>
      {title && <Text style={styles.title}>{title}</Text>}
      <View style={styles.chartContainer}>
        <View style={[styles.chart, { height: chartHeight }]}>
          {data.map((item, index) => renderBar(item, index))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContainer: {
    flex: 1,
  },
  chart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    paddingHorizontal: 10,
  },
  barContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  barWrapper: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
  },
  bar: {
    borderRadius: 4,
    minHeight: 4,
  },
  valueText: {
    fontSize: 12,
    color: '#8E8E93',
    marginBottom: 4,
    fontWeight: '500',
  },
  labelText: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 14,
  },
});

export default SimpleBarChart;
