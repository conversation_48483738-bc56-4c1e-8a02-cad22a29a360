// Gradient Dial Component - Central feature displaying current unit levels

import React, { useEffect, useRef } from 'react';
import { View, Text, Animated } from 'react-native';
import Svg, { Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { GradientDialProps } from '@/types/components';

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const GradientDial: React.FC<GradientDialProps> = ({
  value,
  maxValue,
  size,
  strokeWidth,
  colors,
  backgroundColor = '#F2F2F7',
  showValue = true,
  valueFormatter,
  animated = true,
  animationDuration = 1000,
  threshold,
  thresholdColor = '#FF3B30',
  onValueChange,
  style,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // Calculate progress percentage
  const progress = Math.min(Math.max(value / maxValue, 0), 1);
  const strokeDashoffset = circumference * (1 - progress);

  // Calculate threshold position if provided
  const thresholdProgress = threshold ? Math.min(Math.max(threshold / maxValue, 0), 1) : 0;
  const thresholdOffset = circumference * (1 - thresholdProgress);

  // Animate the dial
  useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: progress,
        duration: animationDuration,
        useNativeDriver: false,
      }).start();
    } else {
      animatedValue.setValue(progress);
    }
  }, [progress, animated, animationDuration, animatedValue]);

  // Format the display value
  const displayValue = valueFormatter ? valueFormatter(value) : value.toString();

  // Determine if we're below threshold
  const isBelowThreshold = threshold && value <= threshold;

  // Choose colors based on threshold
  const dialColors = isBelowThreshold ? [thresholdColor, thresholdColor] : colors;

  return (
    <View style={[{ width: size, height: size }, style]}>
      <Svg width={size} height={size}>
        <Defs>
          <LinearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            {dialColors.map((color, index) => (
              <Stop
                key={index}
                offset={`${(index / (dialColors.length - 1)) * 100}%`}
                stopColor={color}
              />
            ))}
          </LinearGradient>
        </Defs>

        {/* Background Circle */}
        <Circle
          cx={center}
          cy={center}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />

        {/* Threshold Indicator */}
        {threshold && (
          <Circle
            cx={center}
            cy={center}
            r={radius}
            stroke={thresholdColor}
            strokeWidth={2}
            fill="transparent"
            strokeDasharray={`2 ${circumference - 2}`}
            strokeDashoffset={thresholdOffset}
            transform={`rotate(-90 ${center} ${center})`}
            opacity={0.6}
          />
        )}

        {/* Progress Circle */}
        <AnimatedCircle
          cx={center}
          cy={center}
          r={radius}
          stroke="url(#gradient)"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={animated ? 
            animatedValue.interpolate({
              inputRange: [0, 1],
              outputRange: [circumference, circumference * (1 - progress)],
            }) : strokeDashoffset
          }
          strokeLinecap="round"
          transform={`rotate(-90 ${center} ${center})`}
        />
      </Svg>

      {/* Center Content */}
      {showValue && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{
            fontSize: size * 0.12,
            fontWeight: 'bold',
            color: isBelowThreshold ? thresholdColor : '#000000',
            textAlign: 'center',
          }}>
            {displayValue}
          </Text>
          
          <Text style={{
            fontSize: size * 0.06,
            color: '#8E8E93',
            marginTop: 4,
            textAlign: 'center',
          }}>
            {`${Math.round(progress * 100)}%`}
          </Text>
        </View>
      )}

      {/* Warning Indicator */}
      {isBelowThreshold && (
        <View style={{
          position: 'absolute',
          top: size * 0.15,
          right: size * 0.15,
          backgroundColor: thresholdColor,
          borderRadius: 12,
          width: 24,
          height: 24,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <Text style={{
            color: '#FFFFFF',
            fontSize: 16,
            fontWeight: 'bold',
          }}>
            !
          </Text>
        </View>
      )}
    </View>
  );
};

export default GradientDial;
