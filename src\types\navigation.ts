import { NavigatorScreenParams } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { DrawerScreenProps } from '@react-navigation/drawer';

// Main Navigation Stack
export type RootStackParamList = {
  Dashboard: undefined;
  Purchases: undefined;
  Usage: undefined;
  History: undefined;
  Settings: undefined;
};

// Drawer Navigation
export type DrawerParamList = {
  Dashboard: undefined;
  Purchases: undefined;
  Usage: undefined;
  History: undefined;
  Settings: undefined;
};

// Settings Stack Navigation
export type SettingsStackParamList = {
  SettingsMain: undefined;
  GeneralSettings: undefined;
  AppearanceSettings: undefined;
  ResetOptions: undefined;
};

// Screen Props Types
export type DashboardScreenProps = NativeStackScreenProps<RootStackParamList, 'Dashboard'>;
export type PurchasesScreenProps = NativeStackScreenProps<RootStackParamList, 'Purchases'>;
export type UsageScreenProps = NativeStackScreenProps<RootStackParamList, 'Usage'>;
export type HistoryScreenProps = NativeStackScreenProps<RootStackParamList, 'History'>;
export type SettingsScreenProps = NativeStackScreenProps<RootStackParamList, 'Settings'>;

// Drawer Screen Props
export type DashboardDrawerProps = DrawerScreenProps<DrawerParamList, 'Dashboard'>;
export type PurchasesDrawerProps = DrawerScreenProps<DrawerParamList, 'Purchases'>;
export type UsageDrawerProps = DrawerScreenProps<DrawerParamList, 'Usage'>;
export type HistoryDrawerProps = DrawerScreenProps<DrawerParamList, 'History'>;
export type SettingsDrawerProps = DrawerScreenProps<DrawerParamList, 'Settings'>;

// Settings Screen Props
export type GeneralSettingsScreenProps = NativeStackScreenProps<SettingsStackParamList, 'GeneralSettings'>;
export type AppearanceSettingsScreenProps = NativeStackScreenProps<SettingsStackParamList, 'AppearanceSettings'>;
export type ResetOptionsScreenProps = NativeStackScreenProps<SettingsStackParamList, 'ResetOptions'>;

// Navigation Helper Types
export interface NavigationRoute {
  name: string;
  component: React.ComponentType<any>;
  options?: any;
  initialParams?: any;
}

export interface DrawerItem {
  name: string;
  label: string;
  icon: string;
  component: React.ComponentType<any>;
  options?: any;
}

// Navigation State Types
export interface NavigationState {
  currentRoute: string;
  previousRoute: string | null;
  params: any;
  canGoBack: boolean;
}

// Navigation Actions
export type NavigationAction = 
  | { type: 'NAVIGATE'; payload: { screen: string; params?: any } }
  | { type: 'GO_BACK' }
  | { type: 'RESET'; payload: { screen: string; params?: any } }
  | { type: 'TOGGLE_DRAWER' };

export default RootStackParamList;
