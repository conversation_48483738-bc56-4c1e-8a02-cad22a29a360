// User Slice for Redux Store

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UserSettings, UserState } from '@/types';
import { DEFAULT_SETTINGS } from '@/constants';

// Async thunks
export const initializeUser = createAsyncThunk(
  'user/initialize',
  async (initialSettings?: Partial<UserSettings>) => {
    // Initialize user with default settings or provided settings
    const userSettings: UserSettings = {
      id: Date.now().toString(),
      userId: Date.now().toString(),
      unitType: initialSettings?.unitType || DEFAULT_SETTINGS.unitType,
      customUnitName: initialSettings?.customUnitName,
      currency: initialSettings?.currency || DEFAULT_SETTINGS.currency,
      customCurrencyName: initialSettings?.customCurrencyName,
      costPerUnit: initialSettings?.costPerUnit || DEFAULT_SETTINGS.costPerUnit,
      thresholdLimit: initialSettings?.thresholdLimit || DEFAULT_SETTINGS.thresholdLimit,
      theme: initialSettings?.theme || DEFAULT_SETTINGS.theme,
      fontFamily: initialSettings?.fontFamily || DEFAULT_SETTINGS.fontFamily,
      notificationsEnabled: initialSettings?.notificationsEnabled ?? DEFAULT_SETTINGS.notificationsEnabled,
      notificationTime: initialSettings?.notificationTime || DEFAULT_SETTINGS.notificationTime,
      monthlyResetEnabled: initialSettings?.monthlyResetEnabled ?? DEFAULT_SETTINGS.monthlyResetEnabled,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return userSettings;
  }
);

export const updateUserSettings = createAsyncThunk(
  'user/updateSettings',
  async (updates: Partial<UserSettings>, { getState }) => {
    const state = getState() as { user: UserState };
    const currentUser = state.user.currentUser;

    if (!currentUser) {
      throw new Error('No user found');
    }

    const updatedUser: UserSettings = {
      ...currentUser,
      ...updates,
      updatedAt: new Date(),
    };

    // Here you would typically make an API call to save the settings
    // For now, we'll just return the updated user
    return updatedUser;
  }
);

export const resetUserData = createAsyncThunk(
  'user/resetData',
  async (keepSettings: boolean = false) => {
    if (keepSettings) {
      // Reset only data, keep settings
      return { type: 'RESET_DATA_ONLY' };
    } else {
      // Complete factory reset
      return { type: 'FACTORY_RESET' };
    }
  }
);

// Initial state
const initialState: UserState = {
  currentUser: null,
  isLoading: false,
  error: null,
};

// User slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<UserSettings>) => {
      state.currentUser = action.payload;
      state.error = null;
    },
    updateUser: (state, action: PayloadAction<Partial<UserSettings>>) => {
      if (state.currentUser) {
        state.currentUser = {
          ...state.currentUser,
          ...action.payload,
          updatedAt: new Date(),
        };
      }
    },
    clearUser: (state) => {
      state.currentUser = null;
      state.error = null;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    // Settings-specific actions
    updateUnitType: (state, action: PayloadAction<{ unitType: string; customUnitName?: string }>) => {
      if (state.currentUser) {
        state.currentUser.unitType = action.payload.unitType;
        state.currentUser.customUnitName = action.payload.customUnitName;
        state.currentUser.updatedAt = new Date();
      }
    },
    updateCurrency: (state, action: PayloadAction<{ currency: string; customCurrencyName?: string }>) => {
      if (state.currentUser) {
        state.currentUser.currency = action.payload.currency;
        state.currentUser.customCurrencyName = action.payload.customCurrencyName;
        state.currentUser.updatedAt = new Date();
      }
    },
    updateCostPerUnit: (state, action: PayloadAction<number>) => {
      if (state.currentUser) {
        state.currentUser.costPerUnit = action.payload;
        state.currentUser.updatedAt = new Date();
      }
    },
    updateThresholdLimit: (state, action: PayloadAction<number>) => {
      if (state.currentUser) {
        state.currentUser.thresholdLimit = action.payload;
        state.currentUser.updatedAt = new Date();
      }
    },
    updateTheme: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.theme = action.payload;
        state.currentUser.updatedAt = new Date();
      }
    },
    updateFontFamily: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.fontFamily = action.payload;
        state.currentUser.updatedAt = new Date();
      }
    },
    updateNotificationSettings: (state, action: PayloadAction<{ enabled: boolean; time?: string }>) => {
      if (state.currentUser) {
        state.currentUser.notificationsEnabled = action.payload.enabled;
        if (action.payload.time) {
          state.currentUser.notificationTime = action.payload.time;
        }
        state.currentUser.updatedAt = new Date();
      }
    },
    updateMonthlyReset: (state, action: PayloadAction<boolean>) => {
      if (state.currentUser) {
        state.currentUser.monthlyResetEnabled = action.payload;
        state.currentUser.updatedAt = new Date();
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize user
      .addCase(initializeUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializeUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
        state.error = null;
      })
      .addCase(initializeUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to initialize user';
      })
      // Update user settings
      .addCase(updateUserSettings.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUserSettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
        state.error = null;
      })
      .addCase(updateUserSettings.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update settings';
      })
      // Reset user data
      .addCase(resetUserData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resetUserData.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.type === 'FACTORY_RESET') {
          state.currentUser = null;
        }
        state.error = null;
      })
      .addCase(resetUserData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to reset data';
      });
  },
});

// Export actions
export const {
  setUser,
  updateUser,
  clearUser,
  setError,
  clearError,
  setLoading,
  updateUnitType,
  updateCurrency,
  updateCostPerUnit,
  updateThresholdLimit,
  updateTheme,
  updateFontFamily,
  updateNotificationSettings,
  updateMonthlyReset,
} = userSlice.actions;

// Export reducer
export default userSlice.reducer;

// Selectors
export const selectCurrentUser = (state: { user: UserState }) => state.user.currentUser;
export const selectUserLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectUserError = (state: { user: UserState }) => state.user.error;
export const selectUserTheme = (state: { user: UserState }) => state.user.currentUser?.theme;
export const selectUserCurrency = (state: { user: UserState }) => state.user.currentUser?.currency;
export const selectUserUnitType = (state: { user: UserState }) => state.user.currentUser?.unitType;
export const selectUserCostPerUnit = (state: { user: UserState }) => state.user.currentUser?.costPerUnit;
export const selectUserThreshold = (state: { user: UserState }) => state.user.currentUser?.thresholdLimit;
export const selectNotificationEnabled = (state: { user: UserState }) => state.user.currentUser?.notificationsEnabled;
export const selectNotificationTime = (state: { user: UserState }) => state.user.currentUser?.notificationTime;
