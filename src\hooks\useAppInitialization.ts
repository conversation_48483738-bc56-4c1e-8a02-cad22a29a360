// App Initialization Hook

import { useEffect } from 'react';
import { useAppDispatch } from '@/store';
import { setUser } from '@/store/slices/userSlice';
import { setPurchases } from '@/store/slices/purchasesSlice';
import { setUsage, updateCurrentUnits } from '@/store/slices/usageSlice';
import { initializeSampleData, calculateCurrentUnitsFromSample } from '@/utils/sampleData';

export const useAppInitialization = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Initialize the app with sample data
    const initializeApp = async () => {
      try {
        const sampleData = initializeSampleData();
        
        // Set user settings
        dispatch(setUser(sampleData.user));
        
        // Set purchases
        dispatch(setPurchases(sampleData.purchases));
        
        // Set usage
        dispatch(setUsage(sampleData.usage));
        
        // Calculate and set current units
        const currentUnits = calculateCurrentUnitsFromSample();
        dispatch(updateCurrentUnits(currentUnits));
        
        console.log('App initialized with sample data');
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [dispatch]);
};

export default useAppInitialization;
