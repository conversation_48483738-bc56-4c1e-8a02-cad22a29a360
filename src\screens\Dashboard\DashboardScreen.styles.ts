// Dashboard Screen Styles

import { StyleSheet } from 'react-native';
import { LAYOUT } from '@/constants';

export const dashboardStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  contentContainer: {
    paddingBottom: LAYOUT.SPACING.XL,
  },
  header: {
    paddingHorizontal: LAYOUT.SPACING.LG,
    paddingVertical: LAYOUT.SPACING.MD,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: LAYOUT.SPACING.XS,
  },
  subtitleText: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
  },
  dialSection: {
    alignItems: 'center',
    paddingVertical: LAYOUT.SPACING.XL,
    paddingHorizontal: LAYOUT.SPACING.LG,
  },
  dialInfo: {
    alignItems: 'center',
    marginTop: LAYOUT.SPACING.LG,
  },
  currentUnitsLabel: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: LAYOUT.SPACING.XS,
  },
  currentUnitsValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#000000',
  },
  infoSection: {
    paddingHorizontal: LAYOUT.SPACING.LG,
    marginBottom: LAYOUT.SPACING.LG,
  },
  quickActionsSection: {
    paddingHorizontal: LAYOUT.SPACING.LG,
    marginBottom: LAYOUT.SPACING.LG,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: LAYOUT.SPACING.MD,
  },
  quickActionsContainer: {
    gap: LAYOUT.SPACING.MD,
  },
  quickActionButton: {
    marginBottom: LAYOUT.SPACING.SM,
  },
  statusSection: {
    paddingHorizontal: LAYOUT.SPACING.LG,
    backgroundColor: '#FFFFFF',
    marginHorizontal: LAYOUT.SPACING.LG,
    borderRadius: LAYOUT.CARD_BORDER_RADIUS,
    padding: LAYOUT.SPACING.LG,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: LAYOUT.SPACING.SM,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  statusLabel: {
    fontSize: 16,
    color: '#8E8E93',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
});
