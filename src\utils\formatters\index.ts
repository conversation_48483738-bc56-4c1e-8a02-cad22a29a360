// Formatting Utilities for Prepaid Electricity App

import { CURRENCIES } from '@/constants';

/**
 * Format currency values with proper symbol and decimal places
 */
export const formatCurrency = (
  amount: number,
  currencyCode: string = 'USD',
  decimals: number = 2
): string => {
  if (isNaN(amount) || !isFinite(amount)) {
    return '0.00';
  }

  const currency = CURRENCIES.find(c => c.value === currencyCode);
  const symbol = currency?.symbol || '$';
  
  const formattedAmount = Math.abs(amount).toFixed(decimals);
  const parts = formattedAmount.split('.');
  
  // Add thousand separators
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  
  const result = parts.join('.');
  return `${symbol}${result}`;
};

/**
 * Format unit values with proper unit type
 */
export const formatUnits = (
  units: number,
  unitType: string = 'Units',
  decimals: number = 2
): string => {
  if (isNaN(units) || !isFinite(units)) {
    return `0.00 ${unitType}`;
  }

  const formattedUnits = Math.abs(units).toFixed(decimals);
  return `${formattedUnits} ${unitType}`;
};

/**
 * Format percentage values
 */
export const formatPercentage = (
  value: number,
  decimals: number = 1
): string => {
  if (isNaN(value) || !isFinite(value)) {
    return '0.0%';
  }

  return `${(value * 100).toFixed(decimals)}%`;
};

/**
 * Format date values for display
 */
export const formatDate = (
  date: Date | string | number,
  format: 'short' | 'long' | 'time' | 'datetime' = 'short'
): string => {
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  const options: Intl.DateTimeFormatOptions = {};

  switch (format) {
    case 'short':
      options.year = 'numeric';
      options.month = 'short';
      options.day = 'numeric';
      break;
    case 'long':
      options.year = 'numeric';
      options.month = 'long';
      options.day = 'numeric';
      options.weekday = 'long';
      break;
    case 'time':
      options.hour = '2-digit';
      options.minute = '2-digit';
      break;
    case 'datetime':
      options.year = 'numeric';
      options.month = 'short';
      options.day = 'numeric';
      options.hour = '2-digit';
      options.minute = '2-digit';
      break;
  }

  return dateObj.toLocaleDateString('en-US', options);
};

/**
 * Format time duration in human-readable format
 */
export const formatDuration = (
  milliseconds: number
): string => {
  if (isNaN(milliseconds) || milliseconds < 0) {
    return '0 seconds';
  }

  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days} day${days !== 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  } else {
    return `${seconds} second${seconds !== 1 ? 's' : ''}`;
  }
};

/**
 * Format file size in human-readable format
 */
export const formatFileSize = (
  bytes: number,
  decimals: number = 2
): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Format numbers with thousand separators
 */
export const formatNumber = (
  number: number,
  decimals: number = 0
): string => {
  if (isNaN(number) || !isFinite(number)) {
    return '0';
  }

  return number.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * Format cost per unit with currency
 */
export const formatCostPerUnit = (
  cost: number,
  currencyCode: string = 'USD',
  unitType: string = 'Units'
): string => {
  const formattedCost = formatCurrency(cost, currencyCode, 4);
  return `${formattedCost} per ${unitType}`;
};

/**
 * Format efficiency metrics
 */
export const formatEfficiency = (
  unitsPerCurrency: number,
  unitType: string = 'Units',
  currencyCode: string = 'USD'
): string => {
  const currency = CURRENCIES.find(c => c.value === currencyCode);
  const symbol = currency?.symbol || '$';
  
  return `${formatNumber(unitsPerCurrency, 2)} ${unitType} per ${symbol}1`;
};

/**
 * Format relative time (e.g., "2 hours ago", "in 3 days")
 */
export const formatRelativeTime = (
  date: Date | string | number
): string => {
  const dateObj = new Date(date);
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (Math.abs(diffDays) > 7) {
    return formatDate(dateObj, 'short');
  } else if (diffDays > 0) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else if (diffDays < 0) {
    return `in ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffHours < 0) {
    return `in ${Math.abs(diffHours)} hour${Math.abs(diffHours) !== 1 ? 's' : ''}`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffMinutes < 0) {
    return `in ${Math.abs(diffMinutes)} minute${Math.abs(diffMinutes) !== 1 ? 's' : ''}`;
  } else {
    return 'just now';
  }
};

/**
 * Format phone numbers
 */
export const formatPhoneNumber = (
  phoneNumber: string
): string => {
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  return phoneNumber;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (
  text: string,
  maxLength: number,
  ellipsis: string = '...'
): string => {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.slice(0, maxLength - ellipsis.length) + ellipsis;
};

/**
 * Capitalize first letter of each word
 */
export const capitalizeWords = (
  text: string
): string => {
  return text.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

/**
 * Format validation errors for display
 */
export const formatValidationError = (
  error: string | string[]
): string => {
  if (Array.isArray(error)) {
    return error.join(', ');
  }
  return error;
};

export default {
  formatCurrency,
  formatUnits,
  formatPercentage,
  formatDate,
  formatDuration,
  formatFileSize,
  formatNumber,
  formatCostPerUnit,
  formatEfficiency,
  formatRelativeTime,
  formatPhoneNumber,
  truncateText,
  capitalizeWords,
  formatValidationError,
};
