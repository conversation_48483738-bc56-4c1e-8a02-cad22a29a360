import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';

export default function App() {
  const [currentUnits, setCurrentUnits] = React.useState(45.7);
  const [purchases, setPurchases] = React.useState<any[]>([]);
  const [usage, setUsage] = React.useState<any[]>([]);

  const addPurchase = (amount: number, units: number) => {
    const newPurchase = {
      id: Date.now().toString(),
      date: new Date(),
      amount,
      units,
      costPerUnit: amount / units,
    };
    setPurchases([newPurchase, ...purchases]);
    setCurrentUnits(currentUnits + units);
  };

  const recordUsage = (previousUnits: number, newUnits: number) => {
    const usageAmount = Math.abs(previousUnits - newUnits);
    const newUsage = {
      id: Date.now().toString(),
      date: new Date(),
      previousUnits,
      currentUnits: newUnits,
      usageAmount,
    };
    setUsage([newUsage, ...usage]);
    setCurrentUnits(newUnits);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>PREPAID USER - ELECTRICITY</Text>
        <Text style={styles.subtitle}>Monitor your electricity usage</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Current Units Display */}
        <View style={styles.dialContainer}>
          <View style={styles.dial}>
            <Text style={styles.dialValue}>{currentUnits.toFixed(1)}</Text>
            <Text style={styles.dialLabel}>Units</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => addPurchase(50, 25)}
          >
            <Text style={styles.actionButtonText}>Add Sample Purchase</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => recordUsage(currentUnits, currentUnits - 5)}
          >
            <Text style={styles.actionButtonText}>Record Sample Usage</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Purchases */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Purchases</Text>
          {purchases.length === 0 ? (
            <Text style={styles.emptyText}>No purchases yet</Text>
          ) : (
            purchases.slice(0, 3).map((purchase) => (
              <View key={purchase.id} style={styles.listItem}>
                <Text style={styles.listItemTitle}>
                  ${purchase.amount.toFixed(2)} - {purchase.units} Units
                </Text>
                <Text style={styles.listItemSubtitle}>
                  ${purchase.costPerUnit.toFixed(4)} per unit
                </Text>
              </View>
            ))
          )}
        </View>

        {/* Recent Usage */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Usage</Text>
          {usage.length === 0 ? (
            <Text style={styles.emptyText}>No usage recorded yet</Text>
          ) : (
            usage.slice(0, 3).map((usageEntry) => (
              <View key={usageEntry.id} style={styles.listItem}>
                <Text style={styles.listItemTitle}>
                  Used {usageEntry.usageAmount.toFixed(1)} Units
                </Text>
                <Text style={styles.listItemSubtitle}>
                  From {usageEntry.previousUnits.toFixed(1)} to {usageEntry.currentUnits.toFixed(1)}
                </Text>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#007AFF',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  dialContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  dial: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 8,
    borderColor: '#007AFF',
  },
  dialValue: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  dialLabel: {
    fontSize: 16,
    color: '#8E8E93',
    marginTop: 4,
  },
  quickActions: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  actionButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 30,
  },
  emptyText: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  listItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  listItemSubtitle: {
    fontSize: 14,
    color: '#8E8E93',
  },
});
