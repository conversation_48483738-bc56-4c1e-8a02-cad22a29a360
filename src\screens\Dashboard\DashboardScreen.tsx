// Dashboard Screen - Main screen with gradient dial and quick actions

import React, { useEffect, useMemo } from 'react';
import { View, ScrollView, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppSelector, useAppDispatch } from '@/store';
import { 
  selectCurrentUnits, 
  selectWeeklyTotals, 
  selectMonthlyTotals,
  selectUserThreshold,
  selectUserCurrency,
  selectUserUnitType 
} from '@/store';

// Components
import GradientDial from '@/components/common/GradientDial';
import QuickActionButton from '@/components/common/QuickActionButton';
import InfoCard from '@/components/common/InfoCard';
import Container from '@/components/common/Container';

// Utils
import { ElectricityCalculator } from '@/utils/calculations';
import { formatCurrency, formatUnits } from '@/utils/formatters';

// Constants
import { QUICK_ACTIONS, SCREEN_NAMES } from '@/constants';

// Types
import { DashboardScreenProps } from '@/types/navigation';

// Styles
import { dashboardStyles } from './DashboardScreen.styles';

const DashboardScreen: React.FC<DashboardScreenProps> = () => {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();

  // Selectors
  const currentUnits = useAppSelector(selectCurrentUnits);
  const weeklyTotals = useAppSelector(selectWeeklyTotals);
  const monthlyTotals = useAppSelector(selectMonthlyTotals);
  const threshold = useAppSelector(selectUserThreshold) || 10;
  const currency = useAppSelector(selectUserCurrency) || 'USD';
  const unitType = useAppSelector(selectUserUnitType) || 'Units';

  // Calculate usage since last recording
  const usageSinceLastRecording = useMemo(() => {
    // This would be calculated from the last usage entry
    // For now, returning a placeholder value
    return 5.2;
  }, []);

  // Check for low units warning
  useEffect(() => {
    if (ElectricityCalculator.checkThreshold(currentUnits, threshold)) {
      Alert.alert(
        'Low Units Warning',
        `Your current units (${formatUnits(currentUnits, unitType)}) are below the threshold of ${formatUnits(threshold, unitType)}. Consider purchasing more electricity.`,
        [
          {
            text: 'Later',
            style: 'cancel',
          },
          {
            text: 'Add Purchase',
            onPress: () => navigation.navigate(SCREEN_NAMES.PURCHASES),
          },
        ]
      );
    }
  }, [currentUnits, threshold, unitType, navigation]);

  // Quick action handlers
  const handleQuickAction = (actionType: string) => {
    switch (actionType) {
      case 'Add Purchase':
        navigation.navigate(SCREEN_NAMES.PURCHASES);
        break;
      case 'Record Usage':
        navigation.navigate(SCREEN_NAMES.USAGE);
        break;
      case 'View History':
        navigation.navigate(SCREEN_NAMES.HISTORY);
        break;
      default:
        break;
    }
  };

  return (
    <Container safeArea>
      <ScrollView 
        style={dashboardStyles.container}
        contentContainerStyle={dashboardStyles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <View style={dashboardStyles.header}>
          <Text style={dashboardStyles.welcomeText}>Welcome back!</Text>
          <Text style={dashboardStyles.subtitleText}>
            Monitor your electricity usage
          </Text>
        </View>

        {/* Gradient Dial Section */}
        <View style={dashboardStyles.dialSection}>
          <GradientDial
            value={currentUnits}
            maxValue={100} // This could be dynamic based on user's typical usage
            size={200}
            strokeWidth={20}
            colors={['#007AFF', '#5AC8FA', '#34C759']}
            backgroundColor="#F2F2F7"
            showValue={true}
            valueFormatter={(value) => formatUnits(value, unitType)}
            animated={true}
            animationDuration={1000}
            threshold={threshold}
            thresholdColor="#FF3B30"
          />
          
          <View style={dashboardStyles.dialInfo}>
            <Text style={dashboardStyles.currentUnitsLabel}>Current Units</Text>
            <Text style={dashboardStyles.currentUnitsValue}>
              {formatUnits(currentUnits, unitType)}
            </Text>
          </View>
        </View>

        {/* Usage Information Cards */}
        <View style={dashboardStyles.infoSection}>
          <InfoCard
            title="Usage Since Last Recording"
            value={formatUnits(usageSinceLastRecording, unitType)}
            icon="trending-down"
            color="#FF9500"
          />
          
          <InfoCard
            title="Weekly Usage Total"
            value={formatCurrency(weeklyTotals.usage, currency)}
            icon="calendar"
            color="#34C759"
          />
          
          <InfoCard
            title="Monthly Usage Total"
            value={formatCurrency(monthlyTotals.usage, currency)}
            icon="calendar"
            color="#AF52DE"
          />
        </View>

        {/* Quick Actions Section */}
        <View style={dashboardStyles.quickActionsSection}>
          <Text style={dashboardStyles.sectionTitle}>Quick Actions</Text>
          
          <View style={dashboardStyles.quickActionsContainer}>
            {QUICK_ACTIONS.map((action, index) => (
              <QuickActionButton
                key={index}
                title={action.title}
                icon={action.icon}
                onPress={() => handleQuickAction(action.title)}
                variant="primary"
                style={dashboardStyles.quickActionButton}
              />
            ))}
          </View>
        </View>

        {/* Status Information */}
        <View style={dashboardStyles.statusSection}>
          <View style={dashboardStyles.statusItem}>
            <Text style={dashboardStyles.statusLabel}>Threshold Limit</Text>
            <Text style={dashboardStyles.statusValue}>
              {formatUnits(threshold, unitType)}
            </Text>
          </View>
          
          <View style={dashboardStyles.statusItem}>
            <Text style={dashboardStyles.statusLabel}>Currency</Text>
            <Text style={dashboardStyles.statusValue}>{currency}</Text>
          </View>
          
          <View style={dashboardStyles.statusItem}>
            <Text style={dashboardStyles.statusLabel}>Unit Type</Text>
            <Text style={dashboardStyles.statusValue}>{unitType}</Text>
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

export default DashboardScreen;
