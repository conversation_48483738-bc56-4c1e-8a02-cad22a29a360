// UI Slice for Redux Store

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UIState } from '@/types';

// Initial state
const initialState: UIState = {
  isDrawerOpen: false,
  activeScreen: 'Dashboard',
  isLoading: false,
  toastMessage: null,
};

// UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleDrawer: (state) => {
      state.isDrawerOpen = !state.isDrawerOpen;
    },
    setDrawerOpen: (state, action: PayloadAction<boolean>) => {
      state.isDrawerOpen = action.payload;
    },
    setActiveScreen: (state, action: PayloadAction<string>) => {
      state.activeScreen = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    showToast: (state, action: PayloadAction<string>) => {
      state.toastMessage = action.payload;
    },
    hideToast: (state) => {
      state.toastMessage = null;
    },
    resetUI: (state) => {
      state.isDrawerOpen = false;
      state.activeScreen = 'Dashboard';
      state.isLoading = false;
      state.toastMessage = null;
    },
  },
});

export const {
  toggleDrawer,
  setDrawerOpen,
  setActiveScreen,
  setLoading,
  showToast,
  hideToast,
  resetUI,
} = uiSlice.actions;

export default uiSlice.reducer;
