// Electricity Calculation Engine for Prepaid Electricity App

import { PurchaseEntry, UsageEntry, PeriodTotals } from '@/types';
import { startOfWeek, endOfWeek, startOfMonth, endOfMonth, isWithinInterval } from 'date-fns';

export class ElectricityCalculator {
  /**
   * Calculate usage amount from previous and current unit readings
   */
  static calculateUsage(previousUnits: number, currentUnits: number): number {
    if (previousUnits < 0 || currentUnits < 0) {
      throw new Error('Unit values cannot be negative');
    }
    return Math.abs(previousUnits - currentUnits);
  }

  /**
   * Calculate cost per unit from currency amount and units purchased
   */
  static calculateCostPerUnit(currencyAmount: number, units: number): number {
    if (currencyAmount <= 0 || units <= 0) {
      return 0;
    }
    return Number((currencyAmount / units).toFixed(4));
  }

  /**
   * Calculate total cost from units and cost per unit
   */
  static calculateTotalCost(units: number, costPerUnit: number): number {
    if (units < 0 || costPerUnit < 0) {
      return 0;
    }
    return Number((units * costPerUnit).toFixed(2));
  }

  /**
   * Calculate units from currency amount and cost per unit
   */
  static calculateUnitsFromCurrency(currencyAmount: number, costPerUnit: number): number {
    if (currencyAmount <= 0 || costPerUnit <= 0) {
      return 0;
    }
    return Number((currencyAmount / costPerUnit).toFixed(2));
  }

  /**
   * Check if current units are below threshold
   */
  static checkThreshold(currentUnits: number, threshold: number): boolean {
    return currentUnits <= threshold;
  }

  /**
   * Calculate weekly totals for purchases
   */
  static calculateWeeklyPurchaseTotals(
    purchases: PurchaseEntry[],
    referenceDate: Date = new Date()
  ): { totalAmount: number; totalUnits: number; totalSpending: number; count: number } {
    const weekStart = startOfWeek(referenceDate, { weekStartsOn: 1 }); // Monday
    const weekEnd = endOfWeek(referenceDate, { weekStartsOn: 1 });

    const weeklyPurchases = purchases.filter(purchase =>
      isWithinInterval(new Date(purchase.date), { start: weekStart, end: weekEnd })
    );

    return {
      totalAmount: weeklyPurchases.reduce((sum, purchase) => sum + purchase.currencyAmount, 0),
      totalUnits: weeklyPurchases.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0),
      totalSpending: weeklyPurchases.reduce((sum, purchase) => sum + purchase.currencyAmount, 0),
      count: weeklyPurchases.length,
    };
  }

  /**
   * Calculate monthly totals for purchases
   */
  static calculateMonthlyPurchaseTotals(
    purchases: PurchaseEntry[],
    referenceDate: Date = new Date()
  ): { totalAmount: number; totalUnits: number; totalSpending: number; count: number } {
    const monthStart = startOfMonth(referenceDate);
    const monthEnd = endOfMonth(referenceDate);

    const monthlyPurchases = purchases.filter(purchase =>
      isWithinInterval(new Date(purchase.date), { start: monthStart, end: monthEnd })
    );

    return {
      totalAmount: monthlyPurchases.reduce((sum, purchase) => sum + purchase.currencyAmount, 0),
      totalUnits: monthlyPurchases.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0),
      totalSpending: monthlyPurchases.reduce((sum, purchase) => sum + purchase.currencyAmount, 0),
      count: monthlyPurchases.length,
    };
  }

  /**
   * Calculate weekly totals for usage
   */
  static calculateWeeklyUsageTotals(
    usageEntries: UsageEntry[],
    referenceDate: Date = new Date()
  ): { totalUsage: number; count: number; averageUsage: number } {
    const weekStart = startOfWeek(referenceDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(referenceDate, { weekStartsOn: 1 });

    const weeklyUsage = usageEntries.filter(usage =>
      isWithinInterval(new Date(usage.date), { start: weekStart, end: weekEnd })
    );

    const totalUsage = weeklyUsage.reduce((sum, usage) => sum + usage.usageAmount, 0);
    const count = weeklyUsage.length;

    return {
      totalUsage,
      count,
      averageUsage: count > 0 ? Number((totalUsage / count).toFixed(2)) : 0,
    };
  }

  /**
   * Calculate monthly totals for usage
   */
  static calculateMonthlyUsageTotals(
    usageEntries: UsageEntry[],
    referenceDate: Date = new Date()
  ): { totalUsage: number; count: number; averageUsage: number } {
    const monthStart = startOfMonth(referenceDate);
    const monthEnd = endOfMonth(referenceDate);

    const monthlyUsage = usageEntries.filter(usage =>
      isWithinInterval(new Date(usage.date), { start: monthStart, end: monthEnd })
    );

    const totalUsage = monthlyUsage.reduce((sum, usage) => sum + usage.usageAmount, 0);
    const count = monthlyUsage.length;

    return {
      totalUsage,
      count,
      averageUsage: count > 0 ? Number((totalUsage / count).toFixed(2)) : 0,
    };
  }

  /**
   * Calculate period totals (weekly or monthly)
   */
  static calculatePeriodTotals(
    purchases: PurchaseEntry[],
    usageEntries: UsageEntry[],
    period: 'week' | 'month',
    referenceDate: Date = new Date()
  ): PeriodTotals {
    const purchaseTotals = period === 'week' 
      ? this.calculateWeeklyPurchaseTotals(purchases, referenceDate)
      : this.calculateMonthlyPurchaseTotals(purchases, referenceDate);

    const usageTotals = period === 'week'
      ? this.calculateWeeklyUsageTotals(usageEntries, referenceDate)
      : this.calculateMonthlyUsageTotals(usageEntries, referenceDate);

    const startDate = period === 'week' 
      ? startOfWeek(referenceDate, { weekStartsOn: 1 })
      : startOfMonth(referenceDate);

    const endDate = period === 'week'
      ? endOfWeek(referenceDate, { weekStartsOn: 1 })
      : endOfMonth(referenceDate);

    return {
      id: `${period}-${startDate.getTime()}`,
      userId: '', // Will be set by the calling function
      period,
      startDate,
      endDate,
      totalPurchases: purchaseTotals.totalUnits,
      totalUsage: usageTotals.totalUsage,
      totalSpending: purchaseTotals.totalSpending,
      averageCostPerUnit: purchaseTotals.totalUnits > 0 
        ? Number((purchaseTotals.totalSpending / purchaseTotals.totalUnits).toFixed(4))
        : 0,
      entryCount: purchaseTotals.count + usageTotals.count,
    };
  }

  /**
   * Calculate remaining units based on purchases and usage
   */
  static calculateRemainingUnits(
    totalPurchased: number,
    totalUsed: number
  ): number {
    const remaining = totalPurchased - totalUsed;
    return Math.max(0, Number(remaining.toFixed(2)));
  }

  /**
   * Calculate projected usage based on historical data
   */
  static calculateProjectedUsage(
    usageEntries: UsageEntry[],
    days: number = 30
  ): number {
    if (usageEntries.length === 0) return 0;

    const recentEntries = usageEntries
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, days);

    if (recentEntries.length === 0) return 0;

    const totalUsage = recentEntries.reduce((sum, entry) => sum + entry.usageAmount, 0);
    const averageDailyUsage = totalUsage / recentEntries.length;

    return Number((averageDailyUsage * days).toFixed(2));
  }

  /**
   * Calculate estimated days remaining based on current units and usage pattern
   */
  static calculateEstimatedDaysRemaining(
    currentUnits: number,
    usageEntries: UsageEntry[]
  ): number {
    if (currentUnits <= 0) return 0;

    const recentEntries = usageEntries
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 7); // Last 7 days

    if (recentEntries.length === 0) return 0;

    const totalUsage = recentEntries.reduce((sum, entry) => sum + entry.usageAmount, 0);
    const averageDailyUsage = totalUsage / recentEntries.length;

    if (averageDailyUsage <= 0) return 0;

    return Math.floor(currentUnits / averageDailyUsage);
  }

  /**
   * Calculate efficiency metrics
   */
  static calculateEfficiencyMetrics(
    purchases: PurchaseEntry[],
    usageEntries: UsageEntry[]
  ): {
    averageCostPerUnit: number;
    totalSpent: number;
    totalUnitsUsed: number;
    efficiency: number; // units per currency unit
  } {
    const totalSpent = purchases.reduce((sum, purchase) => sum + purchase.currencyAmount, 0);
    const totalUnitsPurchased = purchases.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0);
    const totalUnitsUsed = usageEntries.reduce((sum, usage) => sum + usage.usageAmount, 0);

    const averageCostPerUnit = totalUnitsPurchased > 0 
      ? Number((totalSpent / totalUnitsPurchased).toFixed(4))
      : 0;

    const efficiency = totalSpent > 0 
      ? Number((totalUnitsUsed / totalSpent).toFixed(4))
      : 0;

    return {
      averageCostPerUnit,
      totalSpent: Number(totalSpent.toFixed(2)),
      totalUnitsUsed: Number(totalUnitsUsed.toFixed(2)),
      efficiency,
    };
  }

  /**
   * Validate calculation inputs
   */
  static validateInputs(value: number, min: number = 0, max: number = Infinity): boolean {
    return !isNaN(value) && isFinite(value) && value >= min && value <= max;
  }

  /**
   * Round to specified decimal places
   */
  static roundToDecimal(value: number, decimals: number = 2): number {
    return Number(Math.round(Number(value + 'e' + decimals)) + 'e-' + decimals);
  }
}

export default ElectricityCalculator;
