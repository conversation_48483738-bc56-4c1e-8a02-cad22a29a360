// Core Data Models for Prepaid Electricity App

export interface UserSettings {
  id: string;
  userId: string;
  unitType: 'Units' | 'KWh' | string;
  customUnitName?: string;
  currency: 'USD' | 'EUR' | 'GBP' | 'ZAR' | 'NGN' | string;
  customCurrencyName?: string;
  costPerUnit: number;
  thresholdLimit: number;
  theme: string;
  fontFamily: string;
  notificationsEnabled: boolean;
  notificationTime: string;
  monthlyResetEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PurchaseEntry {
  id: string;
  userId: string;
  date: Date;
  currencyAmount: number;
  unitsPurchased: number;
  costPerUnit: number;
  currency: string;
  notes?: string;
  timestamp: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UsageEntry {
  id: string;
  userId: string;
  date: Date;
  previousUnits: number;
  currentUnits: number;
  usageAmount: number;
  notes?: string;
  timestamp: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PeriodTotals {
  id: string;
  userId: string;
  period: 'week' | 'month';
  startDate: Date;
  endDate: Date;
  totalPurchases: number;
  totalUsage: number;
  totalSpending: number;
  averageCostPerUnit: number;
  entryCount: number;
}

// UI Component Props
export interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'small' | 'medium' | 'large' | 'narrow';
  gradient?: boolean;
  disabled?: boolean;
  onPress: () => void;
  children: React.ReactNode;
  style?: any;
}

export interface CardProps {
  variant: 'elevated' | 'outlined' | 'filled';
  gradient?: boolean;
  interactive?: boolean;
  children: React.ReactNode;
  style?: any;
}

export interface InputProps {
  label: string;
  type: 'text' | 'number' | 'currency';
  value: string | number;
  onChangeText: (text: string) => void;
  validation?: boolean;
  boldBorder: boolean;
  livePreview?: boolean;
  placeholder?: string;
  error?: string;
  style?: any;
}

// Quick Action Interface
export interface QuickAction {
  title: 'Add Purchase' | 'Record Usage' | 'View History';
  icon: string;
  navigation: string;
  style: 'narrow-height';
}

// Theme Configuration
export interface ThemeConfig {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    warning: string;
    success: string;
    info: string;
  };
  gradients: {
    primary: string[];
    secondary: string[];
    accent: string[];
  };
}

// Notification Configuration
export interface NotificationConfig {
  enabled: boolean;
  time: string; // 24-hour format
  message: string;
  frequency: 'daily';
  allowSnooze: boolean;
  sound: string;
}

// Navigation Types
export type RootStackParamList = {
  Dashboard: undefined;
  Purchases: undefined;
  Usage: undefined;
  History: undefined;
  Settings: undefined;
  GeneralSettings: undefined;
  AppearanceSettings: undefined;
  ResetOptions: undefined;
};

// Redux State Types
export interface AppState {
  user: UserState;
  purchases: PurchaseState;
  usage: UsageState;
  settings: SettingsState;
  ui: UIState;
}

export interface UserState {
  currentUser: UserSettings | null;
  isLoading: boolean;
  error: string | null;
}

export interface PurchaseState {
  entries: PurchaseEntry[];
  isLoading: boolean;
  error: string | null;
  totals: {
    weekly: number;
    monthly: number;
  };
}

export interface UsageState {
  entries: UsageEntry[];
  currentUnits: number;
  isLoading: boolean;
  error: string | null;
  totals: {
    weekly: number;
    monthly: number;
  };
}

export interface SettingsState {
  theme: ThemeConfig;
  notifications: NotificationConfig;
  isLoading: boolean;
  error: string | null;
}

export interface UIState {
  isDrawerOpen: boolean;
  activeScreen: string;
  isLoading: boolean;
  toastMessage: string | null;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Chart Data Types
export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'area' | 'pie';
  data: ChartDataPoint[];
  colors: string[];
  animate: boolean;
  showGrid: boolean;
  showLabels: boolean;
}

// Form Validation Types
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'currency' | 'date' | 'select';
  value: any;
  rules?: ValidationRule;
  error?: string;
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>;
};

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Constants Types
export type CurrencyType = 'USD' | 'EUR' | 'GBP' | 'ZAR' | 'NGN';
export type UnitType = 'Units' | 'KWh';
export type ThemeType = 'electric-blue' | 'energy-green' | 'power-purple' | 'solar-orange' | 'dark-mode';
export type FontType = 'default' | 'roboto' | 'open-sans' | 'lato' | 'montserrat';

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Storage Types
export interface StorageItem<T> {
  key: string;
  value: T;
  expiry?: Date;
}

// Export all types
export * from './navigation';
export * from './api';
export * from './components';
