// Purchases Slice for Redux Store

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { PurchaseEntry, PurchaseState } from '@/types';
import { ElectricityCalculator } from '@/utils/calculations';

// Async thunks
export const addPurchase = createAsyncThunk(
  'purchases/add',
  async (purchaseData: {
    currencyAmount: number;
    unitsPurchased: number;
    currency: string;
    notes?: string;
  }) => {
    const purchase: PurchaseEntry = {
      id: Date.now().toString(),
      userId: 'current-user', // This would come from auth context
      date: new Date(),
      currencyAmount: purchaseData.currencyAmount,
      unitsPurchased: purchaseData.unitsPurchased,
      costPerUnit: ElectricityCalculator.calculateCostPerUnit(
        purchaseData.currencyAmount,
        purchaseData.unitsPurchased
      ),
      currency: purchaseData.currency,
      notes: purchaseData.notes,
      timestamp: Date.now(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Here you would typically make an API call to save the purchase
    // For now, we'll just return the purchase
    return purchase;
  }
);

export const updatePurchase = createAsyncThunk(
  'purchases/update',
  async (updateData: {
    id: string;
    currencyAmount?: number;
    unitsPurchased?: number;
    currency?: string;
    notes?: string;
  }) => {
    // Here you would typically make an API call to update the purchase
    // For now, we'll just return the update data
    return updateData;
  }
);

export const deletePurchase = createAsyncThunk(
  'purchases/delete',
  async (purchaseId: string) => {
    // Here you would typically make an API call to delete the purchase
    // For now, we'll just return the ID
    return purchaseId;
  }
);

export const loadPurchases = createAsyncThunk(
  'purchases/load',
  async () => {
    // Here you would typically make an API call to load purchases
    // For now, we'll return an empty array
    return [];
  }
);

export const calculateTotals = createAsyncThunk(
  'purchases/calculateTotals',
  async (_, { getState }) => {
    const state = getState() as { purchases: PurchaseState };
    const purchases = state.purchases.entries;

    const weeklyTotals = ElectricityCalculator.calculateWeeklyPurchaseTotals(purchases);
    const monthlyTotals = ElectricityCalculator.calculateMonthlyPurchaseTotals(purchases);

    return {
      weekly: weeklyTotals.totalSpending,
      monthly: monthlyTotals.totalSpending,
      weeklyUnits: weeklyTotals.totalUnits,
      monthlyUnits: monthlyTotals.totalUnits,
    };
  }
);

// Initial state
const initialState: PurchaseState = {
  entries: [],
  isLoading: false,
  error: null,
  totals: {
    weekly: 0,
    monthly: 0,
  },
};

// Purchases slice
const purchasesSlice = createSlice({
  name: 'purchases',
  initialState,
  reducers: {
    setPurchases: (state, action: PayloadAction<PurchaseEntry[]>) => {
      state.entries = action.payload;
      state.error = null;
    },
    addPurchaseLocal: (state, action: PayloadAction<PurchaseEntry>) => {
      state.entries.unshift(action.payload); // Add to beginning for chronological order
      state.error = null;
    },
    updatePurchaseLocal: (state, action: PayloadAction<{ id: string; updates: Partial<PurchaseEntry> }>) => {
      const index = state.entries.findIndex(purchase => purchase.id === action.payload.id);
      if (index !== -1) {
        state.entries[index] = {
          ...state.entries[index],
          ...action.payload.updates,
          updatedAt: new Date(),
        };
      }
    },
    deletePurchaseLocal: (state, action: PayloadAction<string>) => {
      state.entries = state.entries.filter(purchase => purchase.id !== action.payload);
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    updateTotals: (state, action: PayloadAction<{ weekly: number; monthly: number }>) => {
      state.totals = action.payload;
    },
    clearPurchases: (state) => {
      state.entries = [];
      state.totals = { weekly: 0, monthly: 0 };
      state.error = null;
    },
    sortPurchases: (state, action: PayloadAction<'date' | 'amount' | 'units'>) => {
      const sortBy = action.payload;
      state.entries.sort((a, b) => {
        switch (sortBy) {
          case 'date':
            return new Date(b.date).getTime() - new Date(a.date).getTime();
          case 'amount':
            return b.currencyAmount - a.currencyAmount;
          case 'units':
            return b.unitsPurchased - a.unitsPurchased;
          default:
            return 0;
        }
      });
    },
    filterPurchases: (state, action: PayloadAction<{
      startDate?: Date;
      endDate?: Date;
      minAmount?: number;
      maxAmount?: number;
    }>) => {
      // This would typically be handled by a selector, but included for completeness
      const { startDate, endDate, minAmount, maxAmount } = action.payload;
      
      state.entries = state.entries.filter(purchase => {
        const purchaseDate = new Date(purchase.date);
        
        if (startDate && purchaseDate < startDate) return false;
        if (endDate && purchaseDate > endDate) return false;
        if (minAmount && purchase.currencyAmount < minAmount) return false;
        if (maxAmount && purchase.currencyAmount > maxAmount) return false;
        
        return true;
      });
    },
  },
  extraReducers: (builder) => {
    builder
      // Add purchase
      .addCase(addPurchase.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(addPurchase.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entries.unshift(action.payload);
        state.error = null;
      })
      .addCase(addPurchase.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to add purchase';
      })
      // Update purchase
      .addCase(updatePurchase.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePurchase.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.entries.findIndex(purchase => purchase.id === action.payload.id);
        if (index !== -1) {
          state.entries[index] = {
            ...state.entries[index],
            ...action.payload,
            updatedAt: new Date(),
          };
        }
        state.error = null;
      })
      .addCase(updatePurchase.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update purchase';
      })
      // Delete purchase
      .addCase(deletePurchase.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePurchase.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entries = state.entries.filter(purchase => purchase.id !== action.payload);
        state.error = null;
      })
      .addCase(deletePurchase.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to delete purchase';
      })
      // Load purchases
      .addCase(loadPurchases.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadPurchases.fulfilled, (state, action) => {
        state.isLoading = false;
        state.entries = action.payload;
        state.error = null;
      })
      .addCase(loadPurchases.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load purchases';
      })
      // Calculate totals
      .addCase(calculateTotals.fulfilled, (state, action) => {
        state.totals = {
          weekly: action.payload.weekly,
          monthly: action.payload.monthly,
        };
      });
  },
});

// Export actions
export const {
  setPurchases,
  addPurchaseLocal,
  updatePurchaseLocal,
  deletePurchaseLocal,
  setError,
  clearError,
  setLoading,
  updateTotals,
  clearPurchases,
  sortPurchases,
  filterPurchases,
} = purchasesSlice.actions;

// Export reducer
export default purchasesSlice.reducer;

// Selectors
export const selectPurchases = (state: { purchases: PurchaseState }) => state.purchases.entries;
export const selectPurchasesLoading = (state: { purchases: PurchaseState }) => state.purchases.isLoading;
export const selectPurchasesError = (state: { purchases: PurchaseState }) => state.purchases.error;
export const selectPurchasesTotals = (state: { purchases: PurchaseState }) => state.purchases.totals;
export const selectTotalUnits = (state: { purchases: PurchaseState }) => 
  state.purchases.entries.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0);
export const selectTotalSpent = (state: { purchases: PurchaseState }) => 
  state.purchases.entries.reduce((sum, purchase) => sum + purchase.currencyAmount, 0);
export const selectAverageCostPerUnit = (state: { purchases: PurchaseState }) => {
  const totalSpent = state.purchases.entries.reduce((sum, purchase) => sum + purchase.currencyAmount, 0);
  const totalUnits = state.purchases.entries.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0);
  return totalUnits > 0 ? totalSpent / totalUnits : 0;
};
