// Info Card Component

import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';

interface InfoCardProps {
  title: string;
  value: string;
  icon?: string;
  color?: string;
  style?: ViewStyle;
}

const InfoCard: React.FC<InfoCardProps> = ({
  title,
  value,
  icon,
  color = '#007AFF',
  style,
}) => {
  return (
    <View style={[styles.card, style]}>
      <View style={styles.header}>
        {icon && (
          <View style={[styles.iconContainer, { backgroundColor: color }]}>
            <Text style={styles.icon}>📊</Text>
          </View>
        )}
        <Text style={styles.title}>{title}</Text>
      </View>
      <Text style={[styles.value, { color }]}>{value}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  icon: {
    fontSize: 12,
  },
  title: {
    fontSize: 14,
    color: '#8E8E93',
    flex: 1,
  },
  value: {
    fontSize: 20,
    fontWeight: 'bold',
  },
});

export default InfoCard;
