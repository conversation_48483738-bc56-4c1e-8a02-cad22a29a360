// Enhanced Dashboard with Charts

import React, { useMemo } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { useAppSelector } from '@/store';
import { selectPurchases } from '@/store/slices/purchasesSlice';
import { selectUsage } from '@/store/slices/usageSlice';
import { selectUserCurrency, selectUserUnitType } from '@/store/slices/userSlice';
import Container from '@/components/common/Container';
import SimpleBarChart from '@/components/charts/SimpleBarChart';
import SimpleLineChart from '@/components/charts/SimpleLineChart';
import { formatCurrency, formatUnits } from '@/utils/formatters';
import { format, subDays, startOfDay } from 'date-fns';

const DashboardChartsScreen: React.FC = () => {
  const purchases = useAppSelector(selectPurchases);
  const usage = useAppSelector(selectUsage);
  const currency = useAppSelector(selectUserCurrency) || 'USD';
  const unitType = useAppSelector(selectUserUnitType) || 'Units';

  // Prepare data for last 7 days usage chart
  const usageChartData = useMemo(() => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = startOfDay(subDays(new Date(), 6 - i));
      const dayUsage = usage.filter(u => 
        startOfDay(new Date(u.date)).getTime() === date.getTime()
      );
      const totalUsage = dayUsage.reduce((sum, u) => sum + u.usageAmount, 0);
      
      return {
        x: i,
        y: totalUsage,
        label: format(date, 'MMM dd'),
      };
    });
    
    return last7Days;
  }, [usage]);

  // Prepare data for purchases bar chart
  const purchasesChartData = useMemo(() => {
    const last5Purchases = purchases
      .slice(0, 5)
      .reverse()
      .map((purchase, index) => ({
        label: format(new Date(purchase.date), 'MMM dd'),
        value: purchase.currencyAmount,
        color: '#34C759',
      }));
    
    return last5Purchases;
  }, [purchases]);

  // Prepare data for units purchased vs used comparison
  const comparisonData = useMemo(() => {
    const totalPurchased = purchases.reduce((sum, p) => sum + p.unitsPurchased, 0);
    const totalUsed = usage.reduce((sum, u) => sum + u.usageAmount, 0);
    const remaining = totalPurchased - totalUsed;

    return [
      { label: 'Purchased', value: totalPurchased, color: '#007AFF' },
      { label: 'Used', value: totalUsed, color: '#FF9500' },
      { label: 'Remaining', value: Math.max(0, remaining), color: '#34C759' },
    ];
  }, [purchases, usage]);

  return (
    <Container safeArea>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>Analytics Dashboard</Text>
        
        {/* Daily Usage Trend */}
        <SimpleLineChart
          data={usageChartData}
          title="Daily Usage Trend (Last 7 Days)"
          color="#FF9500"
          showPoints={true}
          showGrid={true}
        />

        {/* Recent Purchases */}
        {purchasesChartData.length > 0 && (
          <SimpleBarChart
            data={purchasesChartData}
            title={`Recent Purchases (${currency})`}
            showValues={true}
          />
        )}

        {/* Units Overview */}
        <SimpleBarChart
          data={comparisonData}
          title={`Units Overview (${unitType})`}
          showValues={true}
        />

        {/* Summary Statistics */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Summary Statistics</Text>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Total Purchases:</Text>
            <Text style={styles.statValue}>
              {formatCurrency(
                purchases.reduce((sum, p) => sum + p.currencyAmount, 0),
                currency
              )}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Total Units Purchased:</Text>
            <Text style={styles.statValue}>
              {formatUnits(
                purchases.reduce((sum, p) => sum + p.unitsPurchased, 0),
                unitType
              )}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Total Units Used:</Text>
            <Text style={styles.statValue}>
              {formatUnits(
                usage.reduce((sum, u) => sum + u.usageAmount, 0),
                unitType
              )}
            </Text>
          </View>
          
          <View style={styles.statRow}>
            <Text style={styles.statLabel}>Average Cost per Unit:</Text>
            <Text style={styles.statValue}>
              {formatCurrency(
                purchases.length > 0 
                  ? purchases.reduce((sum, p) => sum + p.currencyAmount, 0) / 
                    purchases.reduce((sum, p) => sum + p.unitsPurchased, 0)
                  : 0,
                currency,
                4
              )}
            </Text>
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 20,
    textAlign: 'center',
  },
  summaryContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginVertical: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  statLabel: {
    fontSize: 16,
    color: '#000000',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
});

export default DashboardChartsScreen;
