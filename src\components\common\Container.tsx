// Container Component

import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

interface ContainerProps {
  children: React.ReactNode;
  safeArea?: boolean;
  padding?: number;
  margin?: number;
  backgroundColor?: string;
  flex?: number;
  center?: boolean;
  style?: ViewStyle;
}

const Container: React.FC<ContainerProps> = ({
  children,
  safeArea = false,
  padding = 0,
  margin = 0,
  backgroundColor = '#F2F2F7',
  flex = 1,
  center = false,
  style,
}) => {
  const containerStyle: ViewStyle = {
    flex,
    backgroundColor,
    padding,
    margin,
    ...(center && {
      justifyContent: 'center',
      alignItems: 'center',
    }),
    ...style,
  };

  if (safeArea) {
    return (
      <SafeAreaView style={containerStyle}>
        {children}
      </SafeAreaView>
    );
  }

  return (
    <View style={containerStyle}>
      {children}
    </View>
  );
};

export default Container;
