// Settings Screen - App configuration

import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectCurrentUser } from '@/store/slices/userSlice';
import { selectTheme, setTheme } from '@/store/slices/settingsSlice';
import Container from '@/components/common/Container';
import { THEMES } from '@/constants';

const SettingsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectCurrentUser);
  const currentTheme = useAppSelector(selectTheme);

  const handleThemeChange = (themeId: string) => {
    dispatch(setTheme(themeId));
  };

  const handleFactoryReset = () => {
    Alert.alert(
      'Factory Reset',
      'This will delete all your data and reset the app to its initial state. This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            // Implement factory reset logic
            Alert.alert('Success', 'App has been reset to factory settings');
          },
        },
      ]
    );
  };

  const handleDataReset = () => {
    Alert.alert(
      'Reset Dashboard Data',
      'This will clear all purchase and usage data but keep your settings. This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset Data',
          style: 'destructive',
          onPress: () => {
            // Implement data reset logic
            Alert.alert('Success', 'Dashboard data has been reset');
          },
        },
      ]
    );
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container}>
        <Text style={styles.title}>Settings</Text>

        {/* General Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>General</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Unit Type</Text>
            <Text style={styles.settingValue}>{user?.unitType || 'Units'}</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Currency</Text>
            <Text style={styles.settingValue}>{user?.currency || 'USD'}</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Cost Per Unit</Text>
            <Text style={styles.settingValue}>${user?.costPerUnit || '0.15'}</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Threshold Limit</Text>
            <Text style={styles.settingValue}>{user?.thresholdLimit || '10'} Units</Text>
          </View>
        </View>

        {/* Appearance Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          
          <Text style={styles.subsectionTitle}>Theme</Text>
          {THEMES.map((theme) => (
            <TouchableOpacity
              key={theme.id}
              style={[
                styles.themeOption,
                currentTheme?.id === theme.id && styles.selectedTheme
              ]}
              onPress={() => handleThemeChange(theme.id)}
            >
              <View style={[styles.themePreview, { backgroundColor: theme.colors.primary }]} />
              <Text style={styles.themeName}>{theme.name}</Text>
              {currentTheme?.id === theme.id && (
                <Text style={styles.selectedIndicator}>✓</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Daily Reminders</Text>
            <Text style={styles.settingValue}>
              {user?.notificationsEnabled ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Reminder Time</Text>
            <Text style={styles.settingValue}>{user?.notificationTime || '18:00'}</Text>
          </View>
        </View>

        {/* Reset Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reset Options</Text>
          
          <TouchableOpacity style={styles.resetButton} onPress={handleDataReset}>
            <Text style={styles.resetButtonText}>Reset Dashboard Data</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.resetButton, styles.dangerButton]} 
            onPress={handleFactoryReset}
          >
            <Text style={[styles.resetButtonText, styles.dangerButtonText]}>
              Factory Reset
            </Text>
          </TouchableOpacity>
        </View>

        {/* App Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>App Version</Text>
            <Text style={styles.settingValue}>1.0.0</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Developer</Text>
            <Text style={styles.settingValue}>Pathfinders2c</Text>
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  settingLabel: {
    fontSize: 16,
    color: '#000000',
  },
  settingValue: {
    fontSize: 16,
    color: '#8E8E93',
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  selectedTheme: {
    backgroundColor: '#E3F2FD',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  themePreview: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
  },
  themeName: {
    fontSize: 16,
    color: '#000000',
    flex: 1,
  },
  selectedIndicator: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: 'bold',
  },
  resetButton: {
    backgroundColor: '#FF9500',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  dangerButton: {
    backgroundColor: '#FF3B30',
  },
  resetButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  dangerButtonText: {
    color: '#FFFFFF',
  },
});

export default SettingsScreen;
