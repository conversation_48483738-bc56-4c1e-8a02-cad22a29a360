// Settings Main Screen - Main settings screen

import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Switch } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectTheme, selectNotifications } from '@/store/slices/settingsSlice';
import { selectUserSettings } from '@/store';
import { updateNotifications } from '@/store/slices/settingsSlice';
import Container from '@/components/common/Container';

const SettingsMainScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const theme = useAppSelector(selectTheme);
  const notifications = useAppSelector(selectNotifications);
  const userSettings = useAppSelector(selectUserSettings);

  const handleNotificationToggle = (enabled: boolean) => {
    dispatch(updateNotifications({ enabled }));
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container}>
        {/* User Info Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>User Settings</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Currency</Text>
            <Text style={styles.settingValue}>{userSettings?.currency || 'USD'}</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Unit Type</Text>
            <Text style={styles.settingValue}>{userSettings?.unitType || 'Units'}</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Cost Per Unit</Text>
            <Text style={styles.settingValue}>${userSettings?.costPerUnit?.toFixed(4) || '0.1500'}</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Threshold Limit</Text>
            <Text style={styles.settingValue}>{userSettings?.thresholdLimit || 10} Units</Text>
          </View>
        </View>

        {/* Notifications Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Enable Notifications</Text>
            <Switch
              value={notifications?.enabled || false}
              onValueChange={handleNotificationToggle}
              trackColor={{ false: '#C6C6C8', true: '#007AFF' }}
              thumbColor="#FFFFFF"
            />
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Notification Time</Text>
            <Text style={styles.settingValue}>{notifications?.time || '18:00'}</Text>
          </View>
        </View>

        {/* Theme Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Appearance</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Current Theme</Text>
            <Text style={styles.settingValue}>{theme?.name || 'Electric Blue'}</Text>
          </View>
        </View>

        {/* App Info Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Information</Text>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Version</Text>
            <Text style={styles.settingValue}>1.0.0</Text>
          </View>
          
          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Build</Text>
            <Text style={styles.settingValue}>Development</Text>
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    padding: 16,
    backgroundColor: '#F2F2F7',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  settingLabel: {
    fontSize: 16,
    color: '#000000',
    flex: 1,
  },
  settingValue: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default SettingsMainScreen;
