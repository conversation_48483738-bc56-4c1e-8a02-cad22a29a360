// Custom Drawer Content Component

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { DrawerContentScrollView, DrawerContentComponentProps } from '@react-navigation/drawer';
import { useAppSelector } from '@/store';
import { selectTheme } from '@/store/slices/settingsSlice';
import { selectCurrentUnits } from '@/store';
import { formatUnits } from '@/utils/formatters';

const CustomDrawerContent: React.FC<DrawerContentComponentProps> = (props) => {
  const theme = useAppSelector(selectTheme);
  const currentUnits = useAppSelector(selectCurrentUnits);

  const menuItems = [
    { name: 'Dashboard', label: 'Dashboard', icon: '🏠' },
    { name: 'Purchases', label: 'Add Purchase', icon: '💰' },
    { name: 'Usage', label: 'Record Usage', icon: '⚡' },
    { name: 'History', label: 'View History', icon: '📊' },
    { name: 'Settings', label: 'Settings', icon: '⚙️' },
  ];

  return (
    <DrawerContentScrollView {...props} style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme?.colors.primary || '#007AFF' }]}>
        <Text style={styles.appName}>PREPAID USER</Text>
        <Text style={styles.appSubtitle}>ELECTRICITY</Text>
        <View style={styles.unitsContainer}>
          <Text style={styles.unitsLabel}>Current Units</Text>
          <Text style={styles.unitsValue}>{formatUnits(currentUnits, 'Units')}</Text>
        </View>
      </View>

      {/* Menu Items */}
      <View style={styles.menuContainer}>
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.name}
            style={[
              styles.menuItem,
              props.state.routeNames[props.state.index] === item.name && styles.activeMenuItem
            ]}
            onPress={() => props.navigation.navigate(item.name)}
          >
            <Text style={styles.menuIcon}>{item.icon}</Text>
            <Text style={[
              styles.menuLabel,
              props.state.routeNames[props.state.index] === item.name && styles.activeMenuLabel
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>Version 1.0.0</Text>
        <Text style={styles.footerText}>© 2024 Pathfinders2c</Text>
      </View>
    </DrawerContentScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    padding: 20,
    paddingTop: 40,
    marginBottom: 20,
  },
  appName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  appSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 16,
  },
  unitsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    padding: 12,
  },
  unitsLabel: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  unitsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  menuContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 4,
  },
  activeMenuItem: {
    backgroundColor: '#E3F2FD',
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 24,
    textAlign: 'center',
  },
  menuLabel: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '500',
  },
  activeMenuLabel: {
    color: '#007AFF',
    fontWeight: '600',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#F2F2F7',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#8E8E93',
    marginBottom: 4,
  },
});

export default CustomDrawerContent;
