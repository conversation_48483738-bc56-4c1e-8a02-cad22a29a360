{"name": "prepaid-electricity-app", "version": "1.0.0", "description": "A modern electricity prepaid meter tracking mobile application", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "test:e2e": "detox test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "bundle": "expo export"}, "dependencies": {"expo": "~53.0.11", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "prettier": "^3.1.1"}, "keywords": ["react-native", "expo", "electricity", "prepaid", "meter", "tracking", "mobile"], "author": "Pathfinders2c", "license": "MIT", "private": true}