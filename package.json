{"name": "prepaid-electricity-app", "version": "1.0.0", "description": "A modern electricity prepaid meter tracking mobile application", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "test:e2e": "detox test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "bundle": "expo export"}, "dependencies": {"expo": "~53.0.11", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "@react-navigation/native": "^6.1.9", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native-stack": "^6.9.17", "react-native-screens": "~4.1.0", "react-native-safe-area-context": "4.12.0", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "@react-native-async-storage/async-storage": "^1.24.0", "react-native-svg": "15.8.0", "expo-notifications": "~0.29.9", "expo-device": "~6.0.2", "expo-constants": "~17.0.3", "date-fns": "^3.0.6", "react-hook-form": "^7.48.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "prettier": "^3.1.1"}, "keywords": ["react-native", "expo", "electricity", "prepaid", "meter", "tracking", "mobile"], "author": "Pathfinders2c", "license": "MIT", "private": true}