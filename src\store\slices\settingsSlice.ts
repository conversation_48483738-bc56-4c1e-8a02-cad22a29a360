// Settings Slice for Redux Store

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SettingsState, ThemeConfig, NotificationConfig } from '@/types';
import { THEMES, DEFAULT_THEME } from '@/constants';

// Initial state
const initialState: SettingsState = {
  theme: THEMES.find(t => t.id === DEFAULT_THEME) || THEMES[0],
  notifications: {
    enabled: true,
    time: '18:00',
    message: 'Don\'t forget to record your electricity usage!',
    frequency: 'daily',
    allowSnooze: true,
    sound: 'default',
  },
  isLoading: false,
  error: null,
};

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<string>) => {
      const theme = THEMES.find(t => t.id === action.payload);
      if (theme) {
        state.theme = theme;
      }
    },
    updateNotifications: (state, action: PayloadAction<Partial<NotificationConfig>>) => {
      state.notifications = {
        ...state.notifications,
        ...action.payload,
      };
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    resetSettings: (state) => {
      state.theme = THEMES.find(t => t.id === DEFAULT_THEME) || THEMES[0];
      state.notifications = initialState.notifications;
      state.error = null;
    },
  },
});

export const {
  setTheme,
  updateNotifications,
  setError,
  clearError,
  setLoading,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;

// Selectors
export const selectTheme = (state: { settings: SettingsState }) => state.settings.theme;
export const selectNotifications = (state: { settings: SettingsState }) => state.settings.notifications;
