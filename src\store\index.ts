// Redux Store Configuration for Prepaid Electricity App

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import slices
import userSlice from './slices/userSlice';
import purchasesSlice from './slices/purchasesSlice';
import usageSlice from './slices/usageSlice';
import settingsSlice from './slices/settingsSlice';
import uiSlice from './slices/uiSlice';

// Import middleware
import { apiMiddleware } from './middleware/apiMiddleware';
import { syncMiddleware } from './middleware/syncMiddleware';

// Persist configuration
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['user', 'purchases', 'usage', 'settings'], // Only persist these slices
  blacklist: ['ui'], // Don't persist UI state
};

// Combine reducers
const rootReducer = combineReducers({
  user: userSlice,
  purchases: purchasesSlice,
  usage: usageSlice,
  settings: settingsSlice,
  ui: uiSlice,
});

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['register'],
      },
    })
    .concat(apiMiddleware)
    .concat(syncMiddleware),
  devTools: __DEV__,
});

// Create persistor
export const persistor = persistStore(store);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Selectors
export const selectUser = (state: RootState) => state.user;
export const selectPurchases = (state: RootState) => state.purchases;
export const selectUsage = (state: RootState) => state.usage;
export const selectSettings = (state: RootState) => state.settings;
export const selectUI = (state: RootState) => state.ui;

// Combined selectors
export const selectCurrentUnits = (state: RootState) => {
  const { entries: purchases } = state.purchases;
  const { entries: usage } = state.usage;
  
  const totalPurchased = purchases.reduce((sum, purchase) => sum + purchase.unitsPurchased, 0);
  const totalUsed = usage.reduce((sum, usageEntry) => sum + usageEntry.usageAmount, 0);
  
  return Math.max(0, totalPurchased - totalUsed);
};

export const selectWeeklyTotals = (state: RootState) => ({
  purchases: state.purchases.totals.weekly,
  usage: state.usage.totals.weekly,
});

export const selectMonthlyTotals = (state: RootState) => ({
  purchases: state.purchases.totals.monthly,
  usage: state.usage.totals.monthly,
});

export const selectIsLoading = (state: RootState) => 
  state.user.isLoading || 
  state.purchases.isLoading || 
  state.usage.isLoading || 
  state.settings.isLoading ||
  state.ui.isLoading;

export const selectHasError = (state: RootState) => 
  state.user.error || 
  state.purchases.error || 
  state.usage.error || 
  state.settings.error;

export const selectTheme = (state: RootState) => state.settings.theme;

export const selectNotificationSettings = (state: RootState) => state.settings.notifications;

export const selectUserSettings = (state: RootState) => state.user.currentUser;

// Action creators for common operations
export const resetStore = () => ({
  type: 'RESET_STORE',
});

export const clearErrors = () => ({
  type: 'CLEAR_ERRORS',
});

// Store enhancers
if (__DEV__) {
  // Development-only store enhancers
  import('flipper-plugin-redux-debugger').then(({ default: reduxDebugger }) => {
    reduxDebugger(store);
  }).catch(() => {
    // Flipper not available, ignore
  });
}

export default store;
