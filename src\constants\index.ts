// App Constants for Prepaid Electricity App

// App Information
export const APP_NAME = 'PREPAID USER - ELECTRICITY';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'A modern electricity prepaid meter tracking mobile application';

// Currency Options
export const CURRENCIES = [
  { label: 'US Dollar (USD)', value: 'USD', symbol: '$' },
  { label: 'Euro (EUR)', value: 'EUR', symbol: '€' },
  { label: 'British Pound (GBP)', value: 'GBP', symbol: '£' },
  { label: 'South African Rand (ZAR)', value: 'ZAR', symbol: 'R' },
  { label: 'Nigerian Naira (NGN)', value: 'NGN', symbol: '₦' },
] as const;

export const DEFAULT_CURRENCY = 'USD';

// Unit Types
export const UNIT_TYPES = [
  { label: 'Units', value: 'Units' },
  { label: 'Kilowatt Hours (KWh)', value: 'KWh' },
  { label: 'Custom', value: 'Custom' },
] as const;

export const DEFAULT_UNIT_TYPE = 'Units';

// Theme Options
export const THEMES = [
  {
    id: 'electric-blue',
    name: 'Electric Blue',
    colors: {
      primary: '#007AFF',
      secondary: '#5AC8FA',
      accent: '#FF9500',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      border: '#C6C6C8',
      error: '#FF3B30',
      warning: '#FF9500',
      success: '#34C759',
      info: '#007AFF',
    },
    gradients: {
      primary: ['#007AFF', '#5AC8FA'],
      secondary: ['#5AC8FA', '#007AFF'],
      accent: ['#FF9500', '#FFCC02'],
    },
  },
  {
    id: 'energy-green',
    name: 'Energy Green',
    colors: {
      primary: '#34C759',
      secondary: '#30D158',
      accent: '#FF9500',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      border: '#C6C6C8',
      error: '#FF3B30',
      warning: '#FF9500',
      success: '#34C759',
      info: '#007AFF',
    },
    gradients: {
      primary: ['#34C759', '#30D158'],
      secondary: ['#30D158', '#34C759'],
      accent: ['#FF9500', '#FFCC02'],
    },
  },
  {
    id: 'power-purple',
    name: 'Power Purple',
    colors: {
      primary: '#AF52DE',
      secondary: '#BF5AF2',
      accent: '#FF9500',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      border: '#C6C6C8',
      error: '#FF3B30',
      warning: '#FF9500',
      success: '#34C759',
      info: '#007AFF',
    },
    gradients: {
      primary: ['#AF52DE', '#BF5AF2'],
      secondary: ['#BF5AF2', '#AF52DE'],
      accent: ['#FF9500', '#FFCC02'],
    },
  },
  {
    id: 'solar-orange',
    name: 'Solar Orange',
    colors: {
      primary: '#FF9500',
      secondary: '#FFCC02',
      accent: '#007AFF',
      background: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textSecondary: '#8E8E93',
      border: '#C6C6C8',
      error: '#FF3B30',
      warning: '#FF9500',
      success: '#34C759',
      info: '#007AFF',
    },
    gradients: {
      primary: ['#FF9500', '#FFCC02'],
      secondary: ['#FFCC02', '#FF9500'],
      accent: ['#007AFF', '#5AC8FA'],
    },
  },
  {
    id: 'dark-mode',
    name: 'Dark Mode',
    colors: {
      primary: '#0A84FF',
      secondary: '#64D2FF',
      accent: '#FF9F0A',
      background: '#000000',
      surface: '#1C1C1E',
      text: '#FFFFFF',
      textSecondary: '#8E8E93',
      border: '#38383A',
      error: '#FF453A',
      warning: '#FF9F0A',
      success: '#32D74B',
      info: '#64D2FF',
    },
    gradients: {
      primary: ['#0A84FF', '#64D2FF'],
      secondary: ['#64D2FF', '#0A84FF'],
      accent: ['#FF9F0A', '#FFCC02'],
    },
  },
] as const;

export const DEFAULT_THEME = 'electric-blue';

// Font Options
export const FONTS = [
  { label: 'Default', value: 'default' },
  { label: 'Roboto', value: 'roboto' },
  { label: 'Open Sans', value: 'open-sans' },
  { label: 'Lato', value: 'lato' },
  { label: 'Montserrat', value: 'montserrat' },
] as const;

export const DEFAULT_FONT = 'default';

// Default Settings
export const DEFAULT_SETTINGS = {
  unitType: DEFAULT_UNIT_TYPE,
  currency: DEFAULT_CURRENCY,
  costPerUnit: 0.15,
  thresholdLimit: 10,
  theme: DEFAULT_THEME,
  fontFamily: DEFAULT_FONT,
  notificationsEnabled: true,
  notificationTime: '18:00',
  monthlyResetEnabled: true,
};

// Navigation Constants
export const SCREEN_NAMES = {
  DASHBOARD: 'Dashboard',
  PURCHASES: 'Purchases',
  USAGE: 'Usage',
  HISTORY: 'History',
  SETTINGS: 'Settings',
  GENERAL_SETTINGS: 'GeneralSettings',
  APPEARANCE_SETTINGS: 'AppearanceSettings',
  RESET_OPTIONS: 'ResetOptions',
} as const;

// Quick Actions
export const QUICK_ACTIONS = [
  {
    title: 'Add Purchase',
    icon: 'plus-circle',
    navigation: SCREEN_NAMES.PURCHASES,
    style: 'narrow-height',
  },
  {
    title: 'Record Usage',
    icon: 'activity',
    navigation: SCREEN_NAMES.USAGE,
    style: 'narrow-height',
  },
  {
    title: 'View History',
    icon: 'clock',
    navigation: SCREEN_NAMES.HISTORY,
    style: 'narrow-height',
  },
] as const;

// Chart Configuration
export const CHART_COLORS = [
  '#007AFF',
  '#34C759',
  '#FF9500',
  '#AF52DE',
  '#FF3B30',
  '#5AC8FA',
  '#FFCC02',
  '#BF5AF2',
] as const;

// Validation Constants
export const VALIDATION = {
  MIN_CURRENCY_AMOUNT: 0.01,
  MAX_CURRENCY_AMOUNT: 999999.99,
  MIN_UNITS: 0.01,
  MAX_UNITS: 999999.99,
  MIN_COST_PER_UNIT: 0.001,
  MAX_COST_PER_UNIT: 999.99,
  MIN_THRESHOLD: 1,
  MAX_THRESHOLD: 1000,
  MAX_NOTES_LENGTH: 500,
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  USER_SETTINGS: '@prepaid_electricity:user_settings',
  PURCHASES: '@prepaid_electricity:purchases',
  USAGE: '@prepaid_electricity:usage',
  THEME: '@prepaid_electricity:theme',
  NOTIFICATIONS: '@prepaid_electricity:notifications',
  ONBOARDING_COMPLETED: '@prepaid_electricity:onboarding_completed',
  LAST_SYNC: '@prepaid_electricity:last_sync',
} as const;

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.EXPO_PUBLIC_API_URL || 'https://api.prepaidelectricity.app',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Notification Configuration
export const NOTIFICATION_CONFIG = {
  DAILY_REMINDER_ID: 'daily_usage_reminder',
  LOW_UNITS_WARNING_ID: 'low_units_warning',
  MONTHLY_RESET_ID: 'monthly_reset_notification',
  DEFAULT_SOUND: 'default',
  CHANNEL_ID: 'prepaid_electricity_notifications',
  CHANNEL_NAME: 'Prepaid Electricity Notifications',
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  DISPLAY_WITH_TIME: 'MMM dd, yyyy HH:mm',
  API: 'yyyy-MM-dd',
  API_WITH_TIME: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
  TIME_ONLY: 'HH:mm',
  MONTH_YEAR: 'MMM yyyy',
} as const;

// Animation Durations
export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
  DIAL: 1000,
} as const;

// Layout Constants
export const LAYOUT = {
  HEADER_HEIGHT: 60,
  TAB_BAR_HEIGHT: 80,
  DRAWER_WIDTH: 280,
  CARD_BORDER_RADIUS: 12,
  BUTTON_BORDER_RADIUS: 8,
  INPUT_BORDER_RADIUS: 8,
  SPACING: {
    XS: 4,
    SM: 8,
    MD: 16,
    LG: 24,
    XL: 32,
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection error. Please check your internet connection.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  OFFLINE: 'You are currently offline. Some features may not be available.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  PURCHASE_ADDED: 'Purchase added successfully!',
  USAGE_RECORDED: 'Usage recorded successfully!',
  SETTINGS_UPDATED: 'Settings updated successfully!',
  DATA_EXPORTED: 'Data exported successfully!',
  SYNC_COMPLETED: 'Data synchronized successfully!',
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_EXPORT: true,
  ENABLE_SYNC: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_DARK_MODE: true,
  ENABLE_BIOMETRIC_AUTH: false,
} as const;

// Export all constants
export * from './themes';
export * from './navigation';
