// Usage Screen - Record electricity usage

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, StyleSheet } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { addUsage } from '@/store/slices/usageSlice';
import { selectCurrentUnits } from '@/store';
import { selectUserUnitType } from '@/store/slices/userSlice';
import Container from '@/components/common/Container';
import { ElectricityCalculator } from '@/utils/calculations';
import { formatUnits } from '@/utils/formatters';

const UsageScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const currentUnits = useAppSelector(selectCurrentUnits);
  const unitType = useAppSelector(selectUserUnitType) || 'Units';

  const [previousUnits, setPreviousUnits] = useState(currentUnits.toString());
  const [newCurrentUnits, setNewCurrentUnits] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Calculate usage amount
  const previousValue = parseFloat(previousUnits) || 0;
  const currentValue = parseFloat(newCurrentUnits) || 0;
  const usageAmount = currentValue > 0 ? ElectricityCalculator.calculateUsage(previousValue, currentValue) : 0;

  const handleSubmit = async () => {
    if (!newCurrentUnits) {
      Alert.alert('Error', 'Please enter the current unit reading');
      return;
    }

    if (currentValue < 0) {
      Alert.alert('Error', 'Please enter a valid positive number');
      return;
    }

    setIsLoading(true);

    try {
      await dispatch(addUsage({
        previousUnits: previousValue,
        currentUnits: currentValue,
        notes: notes.trim() || undefined,
      })).unwrap();

      // Reset form
      setPreviousUnits(currentValue.toString());
      setNewCurrentUnits('');
      setNotes('');

      Alert.alert('Success', 'Usage recorded successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to record usage. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Record Usage</Text>
        <Text style={styles.subtitle}>Enter your current meter reading</Text>

        {/* Current Units Display */}
        <View style={styles.currentUnitsContainer}>
          <Text style={styles.currentUnitsLabel}>Last Recorded Reading</Text>
          <Text style={styles.currentUnitsValue}>
            {formatUnits(currentUnits, unitType)}
          </Text>
        </View>

        {/* Previous Units Input (Pre-filled) */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Previous Reading ({unitType})</Text>
          <TextInput
            style={styles.input}
            value={previousUnits}
            onChangeText={setPreviousUnits}
            placeholder="0.00"
            keyboardType="numeric"
            returnKeyType="next"
          />
        </View>

        {/* Current Units Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Current Reading ({unitType})</Text>
          <TextInput
            style={styles.input}
            value={newCurrentUnits}
            onChangeText={setNewCurrentUnits}
            placeholder="0.00"
            keyboardType="numeric"
            returnKeyType="next"
          />
        </View>

        {/* Usage Calculation Display */}
        {usageAmount > 0 && (
          <View style={styles.calculationContainer}>
            <Text style={styles.calculationTitle}>Usage Calculation</Text>
            <Text style={styles.calculationText}>
              Previous: {formatUnits(previousValue, unitType)}
            </Text>
            <Text style={styles.calculationText}>
              Current: {formatUnits(currentValue, unitType)}
            </Text>
            <Text style={styles.calculationResult}>
              Usage: {formatUnits(usageAmount, unitType)}
            </Text>
          </View>
        )}

        {/* Notes Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Notes (Optional)</Text>
          <TextInput
            style={[styles.input, styles.notesInput]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Add any notes about this reading..."
            multiline
            numberOfLines={3}
            returnKeyType="done"
          />
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Recording Usage...' : 'Record Usage'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 32,
  },
  currentUnitsContainer: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 24,
  },
  currentUnitsLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  currentUnitsValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  calculationContainer: {
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  calculationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  calculationText: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  calculationResult: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginTop: 8,
  },
  submitButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#C6C6C8',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default UsageScreen;
