# Development Guide - PREPAID USER ELECTRICITY App

## 🏗️ Architecture Overview

This React Native application follows a modular architecture with clear separation of concerns:

### Core Architecture Principles
- **Component-Based**: Reusable UI components
- **State Management**: Redux Toolkit with persistence
- **Type Safety**: Full TypeScript implementation
- **Mobile-First**: Optimized for mobile devices
- **Scalable**: Modular structure for easy expansion

## 📋 Development Phases

### Phase 1: Foundation ✅
- [x] Project initialization with Expo
- [x] Basic TypeScript configuration
- [x] Core directory structure
- [x] Essential type definitions
- [x] Basic UI implementation

### Phase 2: Core Features 🚧
- [ ] Redux store implementation
- [ ] Navigation system (Drawer + Stack)
- [ ] Purchase entry functionality
- [ ] Usage calculation system
- [ ] Data persistence

### Phase 3: Advanced Features 📋
- [ ] History screen with analytics
- [ ] Settings system
- [ ] Chart integration (Victory Native)
- [ ] Notification system
- [ ] Theme customization

### Phase 4: Polish & Testing 📋
- [ ] Performance optimization
- [ ] Comprehensive testing
- [ ] Bug fixes and refinements
- [ ] Accessibility improvements

### Phase 5: Deployment 📋
- [ ] Production build optimization
- [ ] App store assets
- [ ] Deployment configuration
- [ ] Final APK generation

## 🛠️ Development Setup

### Required Tools
```bash
# Node.js and npm
node --version  # v16+
npm --version   # v8+

# Expo CLI
npm install -g @expo/cli

# Development tools
npm install -g typescript
npm install -g eslint
npm install -g prettier
```

### Environment Configuration
```bash
# Clone repository
git clone <repository-url>
cd prepaid-electricity-app

# Install dependencies
npm install

# Start development server
npm start
```

### IDE Setup (VS Code Recommended)
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 📁 Detailed File Structure

### Component Organization
```
src/components/
├── common/              # Reusable components
│   ├── Button.tsx      # Custom button component
│   ├── Card.tsx        # Card container
│   ├── Input.tsx       # Form input
│   ├── Modal.tsx       # Modal dialogs
│   └── Loading.tsx     # Loading indicators
├── forms/              # Form-specific components
│   ├── PurchaseForm.tsx
│   ├── UsageForm.tsx
│   └── SettingsForm.tsx
└── charts/             # Chart components
    ├── LineChart.tsx
    ├── BarChart.tsx
    └── PieChart.tsx
```

### Screen Components
```
src/screens/
├── Dashboard/
│   ├── DashboardScreen.tsx
│   ├── DashboardScreen.styles.ts
│   └── components/
│       ├── GradientDial.tsx
│       ├── QuickActions.tsx
│       └── UsageCards.tsx
├── Purchases/
│   ├── PurchasesScreen.tsx
│   ├── PurchasesScreen.styles.ts
│   └── components/
│       ├── PurchaseForm.tsx
│       └── LivePreview.tsx
└── [Other screens...]
```

### State Management
```
src/store/
├── index.ts            # Store configuration
├── slices/
│   ├── userSlice.ts    # User settings
│   ├── purchasesSlice.ts
│   ├── usageSlice.ts
│   ├── settingsSlice.ts
│   └── uiSlice.ts
└── middleware/
    ├── apiMiddleware.ts
    └── syncMiddleware.ts
```

## 🎨 Styling Guidelines

### Design System
```typescript
// Theme structure
interface Theme {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    error: string;
  };
  spacing: {
    xs: 4;
    sm: 8;
    md: 16;
    lg: 24;
    xl: 32;
  };
  borderRadius: {
    sm: 4;
    md: 8;
    lg: 12;
    xl: 16;
  };
}
```

### Component Styling
```typescript
// Use StyleSheet.create for performance
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },
  card: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
```

## 🔧 Utility Functions

### Calculation Engine
```typescript
// src/utils/calculations/index.ts
export class ElectricityCalculator {
  static calculateUsage(previous: number, current: number): number {
    return Math.abs(previous - current);
  }

  static calculateCostPerUnit(amount: number, units: number): number {
    return units > 0 ? amount / units : 0;
  }

  static checkThreshold(current: number, threshold: number): boolean {
    return current <= threshold;
  }
}
```

### Formatters
```typescript
// src/utils/formatters/index.ts
export const formatCurrency = (amount: number, currency: string): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatUnits = (units: number, unitType: string): string => {
  return `${units.toFixed(2)} ${unitType}`;
};
```

## 🧪 Testing Strategy

### Unit Testing
```typescript
// __tests__/calculations.test.ts
import { ElectricityCalculator } from '../src/utils/calculations';

describe('ElectricityCalculator', () => {
  test('should calculate usage correctly', () => {
    const usage = ElectricityCalculator.calculateUsage(100, 95);
    expect(usage).toBe(5);
  });

  test('should calculate cost per unit', () => {
    const cost = ElectricityCalculator.calculateCostPerUnit(50, 100);
    expect(cost).toBe(0.5);
  });
});
```

### Component Testing
```typescript
// __tests__/components/Button.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import Button from '../../src/components/common/Button';

describe('Button Component', () => {
  test('should render correctly', () => {
    const { getByText } = render(
      <Button title="Test Button" onPress={() => {}} />
    );
    expect(getByText('Test Button')).toBeTruthy();
  });

  test('should call onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
```

## 📱 Platform-Specific Considerations

### iOS Development
```typescript
// Platform-specific styles
const styles = StyleSheet.create({
  container: {
    ...Platform.select({
      ios: {
        paddingTop: 20, // Account for status bar
      },
      android: {
        paddingTop: StatusBar.currentHeight,
      },
    }),
  },
});
```

### Android Development
```typescript
// Handle Android back button
import { BackHandler } from 'react-native';

useEffect(() => {
  const backAction = () => {
    // Handle back button press
    return true; // Prevent default behavior
  };

  const backHandler = BackHandler.addEventListener(
    'hardwareBackPress',
    backAction
  );

  return () => backHandler.remove();
}, []);
```

## 🚀 Performance Optimization

### Best Practices
1. **Use FlatList for large lists**
2. **Implement lazy loading**
3. **Optimize images**
4. **Use React.memo for expensive components**
5. **Implement proper key props**

### Memory Management
```typescript
// Cleanup subscriptions
useEffect(() => {
  const subscription = someService.subscribe();
  
  return () => {
    subscription.unsubscribe();
  };
}, []);

// Use useCallback for event handlers
const handlePress = useCallback(() => {
  // Handle press
}, [dependency]);
```

## 🔍 Debugging

### Development Tools
```bash
# React Native Debugger
npm install -g react-native-debugger

# Flipper (recommended)
# Download from https://fbflipper.com/

# Chrome DevTools
# Shake device -> Debug -> Debug with Chrome
```

### Logging
```typescript
// Structured logging
const logger = {
  info: (message: string, data?: any) => {
    if (__DEV__) {
      console.log(`[INFO] ${message}`, data);
    }
  },
  error: (message: string, error?: Error) => {
    console.error(`[ERROR] ${message}`, error);
  },
};
```

## 📦 Build & Deployment

### Development Build
```bash
# iOS Simulator
npm run ios

# Android Emulator
npm run android

# Web (for testing)
npm run web
```

### Production Build
```bash
# Configure EAS Build
npm install -g @expo/eas-cli
eas login
eas build:configure

# Build for production
eas build --platform ios
eas build --platform android
```

## 🔧 Troubleshooting

### Common Issues
1. **Metro bundler cache**: `npx react-native start --reset-cache`
2. **Node modules**: `rm -rf node_modules && npm install`
3. **iOS build issues**: `cd ios && pod install`
4. **Android build issues**: `cd android && ./gradlew clean`

### Performance Issues
1. **Large bundle size**: Analyze with `npx react-native bundle --analyze`
2. **Slow navigation**: Check for memory leaks
3. **Slow rendering**: Use React DevTools Profiler

## 📚 Resources

### Documentation
- [React Native Docs](https://reactnative.dev/)
- [Expo Docs](https://docs.expo.dev/)
- [Redux Toolkit](https://redux-toolkit.js.org/)
- [React Navigation](https://reactnavigation.org/)

### Tools
- [React Native Debugger](https://github.com/jhen0409/react-native-debugger)
- [Flipper](https://fbflipper.com/)
- [Reactotron](https://github.com/infinitered/reactotron)

---

This development guide provides a comprehensive overview of the project structure, development practices, and best practices for building the PREPAID USER ELECTRICITY mobile application.
