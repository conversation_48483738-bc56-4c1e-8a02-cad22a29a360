import { ReactNode } from 'react';
import { ViewStyle, TextStyle, ImageStyle } from 'react-native';

// Base Component Props
export interface BaseComponentProps {
  style?: ViewStyle | TextStyle | ImageStyle;
  testID?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
}

// Button Component Types
export interface ButtonProps extends BaseComponentProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'small' | 'medium' | 'large' | 'narrow';
  gradient?: boolean;
  disabled?: boolean;
  loading?: boolean;
  onPress: () => void;
  children: ReactNode;
  icon?: string;
  iconPosition?: 'left' | 'right';
}

// Card Component Types
export interface CardProps extends BaseComponentProps {
  variant: 'elevated' | 'outlined' | 'filled';
  gradient?: boolean;
  interactive?: boolean;
  onPress?: () => void;
  children: ReactNode;
  padding?: number;
  margin?: number;
}

// Input Component Types
export interface InputProps extends BaseComponentProps {
  label: string;
  type: 'text' | 'number' | 'currency' | 'email' | 'password';
  value: string | number;
  onChangeText: (text: string) => void;
  validation?: boolean;
  boldBorder?: boolean;
  livePreview?: boolean;
  placeholder?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  multiline?: boolean;
  numberOfLines?: number;
  maxLength?: number;
  editable?: boolean;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
  autoFocus?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  onSubmitEditing?: () => void;
}

// Gradient Dial Component Types
export interface GradientDialProps extends BaseComponentProps {
  value: number;
  maxValue: number;
  size: number;
  strokeWidth: number;
  colors: string[];
  backgroundColor?: string;
  showValue?: boolean;
  valueFormatter?: (value: number) => string;
  animated?: boolean;
  animationDuration?: number;
  onValueChange?: (value: number) => void;
  threshold?: number;
  thresholdColor?: string;
}

// Chart Component Types
export interface ChartProps extends BaseComponentProps {
  type: 'line' | 'bar' | 'area' | 'pie';
  data: ChartDataPoint[];
  width?: number;
  height?: number;
  colors?: string[];
  animate?: boolean;
  showGrid?: boolean;
  showLabels?: boolean;
  showLegend?: boolean;
  xAxisLabel?: string;
  yAxisLabel?: string;
  onDataPointPress?: (dataPoint: ChartDataPoint) => void;
}

export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
  color?: string;
}

// Modal Component Types
export interface ModalProps extends BaseComponentProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  animationType?: 'slide' | 'fade' | 'none';
  transparent?: boolean;
  onShow?: () => void;
  onDismiss?: () => void;
}

// Toast Component Types
export interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  position?: 'top' | 'bottom' | 'center';
  onHide?: () => void;
  action?: {
    label: string;
    onPress: () => void;
  };
}

// Loading Component Types
export interface LoadingProps extends BaseComponentProps {
  size: 'small' | 'medium' | 'large';
  color?: string;
  overlay?: boolean;
  text?: string;
}

// Header Component Types
export interface HeaderProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  leftIcon?: string;
  rightIcon?: string;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  backgroundColor?: string;
  textColor?: string;
  elevation?: number;
}

// List Item Component Types
export interface ListItemProps extends BaseComponentProps {
  title: string;
  subtitle?: string;
  description?: string;
  leftIcon?: string;
  rightIcon?: string;
  leftComponent?: ReactNode;
  rightComponent?: ReactNode;
  onPress?: () => void;
  disabled?: boolean;
  divider?: boolean;
}

// Switch Component Types
export interface SwitchProps extends BaseComponentProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  disabled?: boolean;
  trackColor?: {
    false: string;
    true: string;
  };
  thumbColor?: string;
  size?: 'small' | 'medium' | 'large';
}

// Picker Component Types
export interface PickerProps extends BaseComponentProps {
  label: string;
  value: string | number;
  onValueChange: (value: string | number) => void;
  items: PickerItem[];
  placeholder?: string;
  error?: string;
  disabled?: boolean;
}

export interface PickerItem {
  label: string;
  value: string | number;
  color?: string;
}

// Date Picker Component Types
export interface DatePickerProps extends BaseComponentProps {
  label: string;
  value: Date;
  onDateChange: (date: Date) => void;
  mode?: 'date' | 'time' | 'datetime';
  minimumDate?: Date;
  maximumDate?: Date;
  format?: string;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
}

// Slider Component Types
export interface SliderProps extends BaseComponentProps {
  value: number;
  onValueChange: (value: number) => void;
  minimumValue: number;
  maximumValue: number;
  step?: number;
  trackColor?: string;
  thumbColor?: string;
  minimumTrackTintColor?: string;
  maximumTrackTintColor?: string;
  disabled?: boolean;
  showValue?: boolean;
  valueFormatter?: (value: number) => string;
}

// Badge Component Types
export interface BadgeProps extends BaseComponentProps {
  value: string | number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  textColor?: string;
  variant?: 'filled' | 'outlined';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

// Avatar Component Types
export interface AvatarProps extends BaseComponentProps {
  source?: { uri: string } | number;
  size: number;
  name?: string;
  backgroundColor?: string;
  textColor?: string;
  onPress?: () => void;
  badge?: BadgeProps;
}

// Divider Component Types
export interface DividerProps extends BaseComponentProps {
  orientation?: 'horizontal' | 'vertical';
  thickness?: number;
  color?: string;
  margin?: number;
}

// Spacer Component Types
export interface SpacerProps {
  size: number;
  horizontal?: boolean;
}

// Container Component Types
export interface ContainerProps extends BaseComponentProps {
  children: ReactNode;
  padding?: number;
  margin?: number;
  backgroundColor?: string;
  flex?: number;
  center?: boolean;
  safeArea?: boolean;
}

// Form Component Types
export interface FormProps extends BaseComponentProps {
  children: ReactNode;
  onSubmit: (data: any) => void;
  validationSchema?: any;
  initialValues?: any;
  enableReinitialize?: boolean;
}

// Quick Action Component Types
export interface QuickActionProps extends BaseComponentProps {
  title: string;
  icon: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'secondary';
}

// Theme Provider Types
export interface ThemeProviderProps {
  theme: ThemeConfig;
  children: ReactNode;
}

export interface ThemeConfig {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    warning: string;
    success: string;
    info: string;
  };
  gradients: {
    primary: string[];
    secondary: string[];
    accent: string[];
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
      xxl: number;
    };
    fontWeight: {
      light: string;
      normal: string;
      medium: string;
      bold: string;
    };
  };
}
