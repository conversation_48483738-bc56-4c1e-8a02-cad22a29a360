# PREPAID USER - ELECTRICITY Mobile App

A modern, user-friendly electricity prepaid meter tracking mobile application for Android and iOS. The app helps users monitor electricity consumption, track purchases, manage usage patterns, and receive intelligent notifications.

## 🎯 Features

### Core Functionality
- **Modern Gradient Dial**: Central feature displaying current unit levels with smooth animations
- **Purchase Tracking**: Log electricity purchases with real-time cost calculations
- **Usage Recording**: Manual unit entry with automatic usage calculation
- **Comprehensive History**: Detailed logs with weekly and monthly totals
- **Smart Settings**: Customizable themes, currencies, and notification preferences

### Key Screens
1. **Dashboard**: Central command center with gradient dial and quick actions
2. **Purchases**: Add electricity purchases with live cost preview
3. **Usage**: Record meter readings with automatic calculations
4. **History**: View all transactions with filtering and analytics
5. **Settings**: Comprehensive app configuration

## 🛠️ Technical Stack

### Frontend
- **Framework**: React Native with Expo
- **Language**: TypeScript
- **Navigation**: React Navigation v6 (Drawer Navigation)
- **State Management**: Redux Toolkit with Redux Persist
- **Styling**: <PERSON>agu<PERSON> (planned) / React Native StyleSheet
- **Charts**: Victory Native (planned)

### Backend (Planned)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: Supabase
- **Authentication**: Firebase Auth

## 📱 Current Implementation

The current version includes a basic working prototype with:

### ✅ Implemented Features
- Basic UI with gradient dial display
- Sample purchase and usage tracking
- Real-time unit calculations
- Responsive design for mobile devices
- Clean, modern interface

### 🚧 In Development
- Full Redux store implementation
- Navigation system with drawer menu
- Advanced chart visualizations
- Notification system
- Data persistence
- Theme customization

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (for testing)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd prepaid-electricity-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   ```bash
   # iOS
   npm run ios
   
   # Android
   npm run android
   
   # Web (for testing)
   npm run web
   ```

## 📁 Project Structure

```
prepaid-electricity-app/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── common/         # Generic components
│   │   ├── forms/          # Form-specific components
│   │   └── charts/         # Chart components
│   ├── screens/            # Screen components
│   │   ├── Dashboard/      # Dashboard screen
│   │   ├── Purchases/      # Purchases screen
│   │   ├── Usage/          # Usage screen
│   │   ├── History/        # History screen
│   │   └── Settings/       # Settings screen
│   ├── navigation/         # Navigation configuration
│   ├── store/              # Redux store setup
│   │   ├── slices/         # Redux slices
│   │   └── middleware/     # Custom middleware
│   ├── services/           # API and external services
│   ├── utils/              # Helper functions
│   │   ├── calculations/   # Electricity calculations
│   │   ├── formatters/     # Data formatting utilities
│   │   └── validators/     # Input validation
│   ├── types/              # TypeScript definitions
│   ├── constants/          # App constants
│   └── styles/             # Global styles and themes
├── assets/                 # Images, fonts, icons
├── __tests__/              # Test files
└── docs/                   # Documentation
```

## 🎨 Design System

### Color Themes
1. **Electric Blue**: Primary blues with electric accents
2. **Energy Green**: Eco-friendly green gradients
3. **Power Purple**: Premium purple with gold accents
4. **Solar Orange**: Warm oranges and yellows
5. **Dark Mode**: Dark theme with neon highlights

### Typography
- **Headers**: Bold, modern sans-serif
- **Body Text**: Clean, readable fonts
- **Numbers**: Monospace for data display

## 📊 Data Models

### Core Entities
```typescript
interface UserSettings {
  unitType: 'Units' | 'KWh' | string;
  currency: 'USD' | 'EUR' | 'GBP' | 'ZAR' | 'NGN';
  costPerUnit: number;
  thresholdLimit: number;
  theme: string;
  notificationsEnabled: boolean;
}

interface PurchaseEntry {
  id: string;
  date: Date;
  currencyAmount: number;
  unitsPurchased: number;
  costPerUnit: number;
  currency: string;
}

interface UsageEntry {
  id: string;
  date: Date;
  previousUnits: number;
  currentUnits: number;
  usageAmount: number;
}
```

## 🧮 Calculation Engine

The app includes a comprehensive calculation engine for:
- Usage amount calculation: `Math.abs(previousUnits - currentUnits)`
- Cost per unit: `currencyAmount / unitsPurchased`
- Weekly and monthly totals
- Efficiency metrics
- Threshold checking

## 🔔 Notification System

### Features (Planned)
- Daily usage entry reminders
- Low units warnings
- Monthly reset notifications
- Customizable notification times

## 🧪 Testing

### Testing Strategy
- **Unit Tests**: Jest + React Native Testing Library
- **Integration Tests**: Detox for E2E testing
- **Performance Testing**: Flipper integration

### Running Tests
```bash
# Unit tests
npm test

# E2E tests (when implemented)
npm run test:e2e

# Type checking
npm run type-check

# Linting
npm run lint
```

## 📱 Deployment

### Build Commands
```bash
# Development build
npm run android
npm run ios

# Production build (when EAS is configured)
npm run build:android
npm run build:ios
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Pathfinders2c**
- Email: <EMAIL>

## 🙏 Acknowledgments

- React Native community for excellent documentation
- Expo team for the amazing development platform
- Redux Toolkit for simplified state management
- All contributors and testers

---

**Note**: This is a comprehensive electricity prepaid meter tracking application designed for mobile devices. The current version includes a working prototype with core functionality. Full feature implementation is ongoing.
