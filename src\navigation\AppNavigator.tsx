// Main App Navigator with Drawer Navigation

import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { useAppSelector } from '../store';
import { selectTheme } from '../store/slices/settingsSlice';

// Screens
import DashboardScreen from '../screens/Dashboard/DashboardScreen';
import DashboardChartsScreen from '../screens/Dashboard/DashboardChartsScreen';
import PurchasesScreen from '../screens/Purchases/PurchasesScreen';
import UsageScreen from '../screens/Usage/UsageScreen';
import HistoryScreen from '../screens/History/HistoryScreen';
import SettingsNavigator from './SettingsNavigator';

// Components
import CustomDrawerContent from '../components/navigation/CustomDrawerContent';

// Types
import { DrawerParamList } from '../types/navigation';

// Constants
import { SCREEN_NAMES } from '../constants';

const Drawer = createDrawerNavigator<DrawerParamList>();

const AppNavigator: React.FC = () => {
  const theme = useAppSelector(selectTheme);

  return (
    <Drawer.Navigator
      initialRouteName="Dashboard"
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: true,
        drawerType: 'front',
        drawerStyle: {
          backgroundColor: theme?.colors.surface || '#FFFFFF',
          width: 280,
        },
        headerStyle: {
          backgroundColor: theme?.colors.primary || '#007AFF',
        },
        headerTintColor: theme?.colors.text || '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 18,
        },
        drawerActiveTintColor: theme?.colors.primary || '#007AFF',
        drawerInactiveTintColor: theme?.colors.textSecondary || '#8E8E93',
        drawerLabelStyle: {
          fontSize: 16,
          fontWeight: '500',
        },
      }}
    >
      <Drawer.Screen
        name={SCREEN_NAMES.DASHBOARD}
        component={DashboardScreen}
        options={{
          title: 'Dashboard',
          drawerLabel: 'Dashboard',
          drawerIcon: ({ color, size }) => (
            <Icon name="home" size={size} color={color} />
          ),
        }}
      />
      
      <Drawer.Screen
        name={SCREEN_NAMES.PURCHASES}
        component={PurchasesScreen}
        options={{
          title: 'Purchases',
          drawerLabel: 'Add Purchase',
          drawerIcon: ({ color, size }) => (
            <Icon name="plus-circle" size={size} color={color} />
          ),
        }}
      />
      
      <Drawer.Screen
        name={SCREEN_NAMES.USAGE}
        component={UsageScreen}
        options={{
          title: 'Usage',
          drawerLabel: 'Record Usage',
          drawerIcon: ({ color, size }) => (
            <Icon name="activity" size={size} color={color} />
          ),
        }}
      />
      
      <Drawer.Screen
        name={SCREEN_NAMES.HISTORY}
        component={HistoryScreen}
        options={{
          title: 'History',
          drawerLabel: 'View History',
          drawerIcon: ({ color, size }) => (
            <Icon name="clock" size={size} color={color} />
          ),
        }}
      />

      <Drawer.Screen
        name="Charts"
        component={DashboardChartsScreen}
        options={{
          title: 'Analytics',
          drawerLabel: 'Analytics',
          drawerIcon: ({ color, size }) => (
            <Icon name="chart" size={size} color={color} />
          ),
        }}
      />

      <Drawer.Screen
        name={SCREEN_NAMES.SETTINGS}
        component={SettingsNavigator}
        options={{
          title: 'Settings',
          drawerLabel: 'Settings',
          drawerIcon: ({ color, size }) => (
            <Icon name="settings" size={size} color={color} />
          ),
          headerShown: false, // Settings navigator will handle its own header
        }}
      />
    </Drawer.Navigator>
  );
};

// Placeholder Icon component (will be replaced with actual icon library)
import { View, Text } from 'react-native';

const Icon: React.FC<{ name: string; size: number; color: string }> = ({ name, size, color }) => {
  const getIconText = (iconName: string) => {
    switch (iconName) {
      case 'home': return '🏠';
      case 'plus-circle': return '➕';
      case 'activity': return '📊';
      case 'clock': return '🕐';
      case 'settings': return '⚙️';
      case 'chart': return '📈';
      default: return '●';
    }
  };

  return (
    <View
      style={{
        width: size,
        height: size,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <Text style={{ fontSize: size * 0.8, color }}>
        {getIconText(name)}
      </Text>
    </View>
  );
};

export default AppNavigator;
