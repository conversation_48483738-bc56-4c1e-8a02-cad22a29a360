// Simple Line Chart Component - Basic implementation without external libraries

import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Svg, { Polyline, Circle, Text as SvgText } from 'react-native-svg';

interface DataPoint {
  x: number;
  y: number;
  label?: string;
}

interface SimpleLineChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  title?: string;
  color?: string;
  showPoints?: boolean;
  showGrid?: boolean;
}

const SimpleLineChart: React.FC<SimpleLineChartProps> = ({
  data,
  width = Dimensions.get('window').width - 40,
  height = 200,
  title,
  color = '#007AFF',
  showPoints = true,
  showGrid = true,
}) => {
  const chartHeight = height - 80; // Reserve space for title and labels
  const chartWidth = width - 40; // Padding
  const padding = 20;

  if (data.length === 0) {
    return (
      <View style={[styles.container, { width, height }]}>
        {title && <Text style={styles.title}>{title}</Text>}
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No data available</Text>
        </View>
      </View>
    );
  }

  // Calculate scales
  const minX = Math.min(...data.map(d => d.x));
  const maxX = Math.max(...data.map(d => d.x));
  const minY = Math.min(...data.map(d => d.y));
  const maxY = Math.max(...data.map(d => d.y));

  const xScale = (x: number) => ((x - minX) / (maxX - minX)) * (chartWidth - 2 * padding) + padding;
  const yScale = (y: number) => chartHeight - padding - ((y - minY) / (maxY - minY)) * (chartHeight - 2 * padding);

  // Generate points for the polyline
  const points = data.map(d => `${xScale(d.x)},${yScale(d.y)}`).join(' ');

  // Grid lines
  const gridLines = [];
  if (showGrid) {
    // Horizontal grid lines
    for (let i = 0; i <= 4; i++) {
      const y = padding + (i * (chartHeight - 2 * padding)) / 4;
      gridLines.push(
        <Polyline
          key={`h-grid-${i}`}
          points={`${padding},${y} ${chartWidth - padding},${y}`}
          stroke="#E5E5EA"
          strokeWidth="1"
        />
      );
    }

    // Vertical grid lines
    for (let i = 0; i <= 4; i++) {
      const x = padding + (i * (chartWidth - 2 * padding)) / 4;
      gridLines.push(
        <Polyline
          key={`v-grid-${i}`}
          points={`${x},${padding} ${x},${chartHeight - padding}`}
          stroke="#E5E5EA"
          strokeWidth="1"
        />
      );
    }
  }

  return (
    <View style={[styles.container, { width, height }]}>
      {title && <Text style={styles.title}>{title}</Text>}
      <Svg width={chartWidth} height={chartHeight}>
        {gridLines}
        
        {/* Main line */}
        <Polyline
          points={points}
          fill="none"
          stroke={color}
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* Data points */}
        {showPoints && data.map((point, index) => (
          <Circle
            key={index}
            cx={xScale(point.x)}
            cy={yScale(point.y)}
            r="4"
            fill={color}
            stroke="#FFFFFF"
            strokeWidth="2"
          />
        ))}

        {/* Y-axis labels */}
        <SvgText
          x="10"
          y={padding}
          fontSize="12"
          fill="#8E8E93"
          textAnchor="middle"
        >
          {maxY.toFixed(1)}
        </SvgText>
        <SvgText
          x="10"
          y={chartHeight - padding}
          fontSize="12"
          fill="#8E8E93"
          textAnchor="middle"
        >
          {minY.toFixed(1)}
        </SvgText>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#8E8E93',
    fontStyle: 'italic',
  },
});

export default SimpleLineChart;
