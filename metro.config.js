const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add support for SVG files
config.transformer.assetPlugins = ['expo-asset/tools/hashAssetFiles'];

// Add support for additional file extensions
config.resolver.assetExts.push('svg');

// Add support for TypeScript path mapping
config.resolver.alias = {
  '@': './src',
  '@/components': './src/components',
  '@/screens': './src/screens',
  '@/navigation': './src/navigation',
  '@/store': './src/store',
  '@/services': './src/services',
  '@/utils': './src/utils',
  '@/types': './src/types',
  '@/constants': './src/constants',
  '@/assets': './src/assets',
  '@/styles': './src/styles',
};

module.exports = config;
