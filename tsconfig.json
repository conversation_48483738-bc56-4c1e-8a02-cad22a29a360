{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/navigation/*": ["src/navigation/*"], "@/store/*": ["src/store/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/constants/*": ["src/constants/*"], "@/assets/*": ["src/assets/*"], "@/styles/*": ["src/styles/*"]}}, "include": ["src/**/*", "App.tsx", "index.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}