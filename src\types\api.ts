// API Request and Response Types

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
  timestamp?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User API Types
export interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  token: string;
  refreshToken: string;
  expiresIn: number;
}

// Purchase API Types
export interface CreatePurchaseRequest {
  currencyAmount: number;
  unitsPurchased: number;
  currency: string;
  notes?: string;
}

export interface UpdatePurchaseRequest extends Partial<CreatePurchaseRequest> {
  id: string;
}

export interface GetPurchasesRequest {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  sortBy?: 'date' | 'amount' | 'units';
  sortOrder?: 'asc' | 'desc';
}

// Usage API Types
export interface CreateUsageRequest {
  previousUnits: number;
  currentUnits: number;
  notes?: string;
}

export interface UpdateUsageRequest extends Partial<CreateUsageRequest> {
  id: string;
}

export interface GetUsageRequest {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  sortBy?: 'date' | 'usage';
  sortOrder?: 'asc' | 'desc';
}

// Settings API Types
export interface UpdateSettingsRequest {
  unitType?: string;
  customUnitName?: string;
  currency?: string;
  customCurrencyName?: string;
  costPerUnit?: number;
  thresholdLimit?: number;
  theme?: string;
  fontFamily?: string;
  notificationsEnabled?: boolean;
  notificationTime?: string;
  monthlyResetEnabled?: boolean;
}

// Analytics API Types
export interface GetAnalyticsRequest {
  period: 'week' | 'month' | 'year';
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
}

export interface AnalyticsResponse {
  totalPurchases: number;
  totalUsage: number;
  totalSpending: number;
  averageCostPerUnit: number;
  trends: {
    purchases: ChartDataPoint[];
    usage: ChartDataPoint[];
    spending: ChartDataPoint[];
  };
  insights: {
    highestUsageDay: string;
    lowestUsageDay: string;
    averageDailyUsage: number;
    projectedMonthlySpending: number;
  };
}

export interface ChartDataPoint {
  x: string | number;
  y: number;
  label?: string;
  date?: string;
}

// Export API Types
export interface ExportRequest {
  format: 'csv' | 'json' | 'pdf';
  type: 'purchases' | 'usage' | 'all';
  startDate?: string;
  endDate?: string;
}

export interface ExportResponse {
  downloadUrl: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
}

// Notification API Types
export interface NotificationRequest {
  title: string;
  body: string;
  data?: any;
  scheduledFor?: string;
}

export interface NotificationResponse {
  id: string;
  status: 'sent' | 'scheduled' | 'failed';
  sentAt?: string;
  scheduledFor?: string;
}

// Error Response Types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  field?: string;
  timestamp: string;
}

export interface ValidationError extends ApiError {
  field: string;
  value: any;
  constraint: string;
}

// HTTP Method Types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// API Configuration Types
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
  retryAttempts: number;
  retryDelay: number;
}

// Request Configuration Types
export interface RequestConfig {
  method: HttpMethod;
  url: string;
  data?: any;
  params?: any;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// Response Interceptor Types
export interface ResponseInterceptor {
  onSuccess: (response: any) => any;
  onError: (error: any) => any;
}

// Request Interceptor Types
export interface RequestInterceptor {
  onRequest: (config: RequestConfig) => RequestConfig;
  onError: (error: any) => any;
}

// API Client Types
export interface ApiClient {
  get<T>(url: string, params?: any): Promise<ApiResponse<T>>;
  post<T>(url: string, data?: any): Promise<ApiResponse<T>>;
  put<T>(url: string, data?: any): Promise<ApiResponse<T>>;
  patch<T>(url: string, data?: any): Promise<ApiResponse<T>>;
  delete<T>(url: string): Promise<ApiResponse<T>>;
}

// Sync Types
export interface SyncStatus {
  lastSyncAt: Date | null;
  pendingChanges: number;
  isOnline: boolean;
  isSyncing: boolean;
}

export interface SyncRequest {
  lastSyncAt?: string;
  changes: {
    purchases: CreatePurchaseRequest[];
    usage: CreateUsageRequest[];
    settings: UpdateSettingsRequest;
  };
}

export interface SyncResponse {
  success: boolean;
  syncedAt: string;
  conflicts: any[];
  serverChanges: {
    purchases: any[];
    usage: any[];
    settings: any;
  };
}
